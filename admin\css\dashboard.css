/**
 * VedMG ClassRoom Dashboard CSS
 * 
 * Specific styles for the dashboard page.
 * Contains styles for statistics cards, navigation cards, and dashboard-specific elements.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

/* Dashboard Statistics Grid */
.vedmg-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.vedmg-stat-card {
    background: #fff;
    padding: 20px;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.vedmg-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.vedmg-stat-number {
    font-size: 36px;
    font-weight: bold;
    color: #0073aa;
    margin-bottom: 8px;
}

.vedmg-stat-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.vedmg-stat-note small.real-data {
    color: #46b450;
    font-weight: 600;
}

.vedmg-stat-note small.no-data {
    color: #999;
    font-style: italic;
}

/* Navigation Grid */
.vedmg-nav-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.vedmg-nav-card {
    background: #fff;
    padding: 25px;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    text-align: center;
}

.vedmg-nav-card:hover,
.vedmg-nav-card.hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
}

.vedmg-nav-icon {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

.vedmg-nav-card h3 {
    margin: 0 0 10px 0;
    color: #23282d;
    font-size: 18px;
}

.vedmg-nav-card p {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 20px;
}

.vedmg-nav-card .vedmg-classroom-btn {
    display: inline-block;
    margin-top: 10px;
}

/* System Status */
.vedmg-system-status {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.vedmg-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 4px;
}

.vedmg-status-label {
    font-weight: 600;
    color: #23282d;
}

.vedmg-status-value {
    color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .vedmg-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .vedmg-nav-grid {
        grid-template-columns: 1fr;
    }
    
    .vedmg-nav-card {
        padding: 20px;
    }
    
    .vedmg-nav-icon {
        font-size: 36px;
    }
    
    .vedmg-stat-number {
        font-size: 28px;
    }
    
    .vedmg-status-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

@media (max-width: 480px) {
    .vedmg-stat-card {
        padding: 15px;
    }
    
    .vedmg-nav-card {
        padding: 15px;
    }
    
    .vedmg-stat-number {
        font-size: 24px;
    }
    
    .vedmg-nav-icon {
        font-size: 32px;
    }
}
