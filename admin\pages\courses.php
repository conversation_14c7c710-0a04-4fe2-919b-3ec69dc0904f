<?php
/**
 * VedMG ClassRoom Course Management Page
 * 
 * This page handles course management functionality.
 * Allows viewing courses, managing Google Classroom integration, and course actions.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

// Include database helper
require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'database/helper.php';

// Log page access
vedmg_log_admin_action('Viewed courses management page');

// Handle pagination and filtering parameters
$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$per_page = isset($_GET['per_page']) ? max(10, min(100, intval($_GET['per_page']))) : 10;
$search_filter = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';

// Get paginated data from database
$course_data = VedMG_ClassRoom_Database_Helper::get_courses($current_page, $per_page, $search_filter);
$courses = $course_data['courses'];
$total_count = $course_data['total_count'];
$total_pages = $course_data['total_pages'];

// Get classroom options for dropdowns
$classroom_options = VedMG_ClassRoom_Database_Helper::get_classroom_options();

// Calculate pagination info
$start_item = (($current_page - 1) * $per_page) + 1;
$end_item = min($current_page * $per_page, $total_count);

// Log filtering if active
if ($search_filter) {
    vedmg_log_info('ADMIN', 'Filtering courses - Search: ' . $search_filter);
}
?>

<div class="vedmg-classroom-admin">
    <!-- Page Header -->
    <div class="vedmg-classroom-header">
        <h1>Course Management</h1>
        <p>Manage your courses, instructors, and Google Classroom integrations</p>
    </div>
    
    <!-- Course Management Section -->
    <div class="vedmg-classroom-section">
        <div class="vedmg-section-header">
            <h2>All Courses</h2>
            
            <!-- Google Classroom Sync Controls -->
            <div class="vedmg-google-classroom-controls">
                <div class="vedmg-instructor-selection">
                    <label for="instructor-dropdown">Select Instructor:</label>
                    <select id="instructor-dropdown" class="vedmg-filter-select">
                        <option value="">-- Select Instructor --</option>
                    </select>
                </div>
                <div class="vedmg-email-override">
                    <label for="manual-email">Or Enter Email:</label>
                    <input type="email" id="manual-email" class="vedmg-filter-input" 
                           placeholder="<EMAIL>" style="width: 200px;">
                </div>
                <button class="vedmg-classroom-btn vedmg-classroom-btn-primary" id="sync-google-classroom" disabled>
                    Sync Google Classroom
                </button>
            </div>
            
            <div class="vedmg-section-actions">
                <button class="vedmg-classroom-btn" id="refresh-courses">
                    Refresh Courses
                </button>
                <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="sync-masterstudy">
                    Sync with MasterStudy LMS
                </button>
            </div>
        </div>
        
        <!-- Course Filter Section -->
        <form method="GET" class="vedmg-filter-controls">
            <input type="hidden" name="page" value="<?php echo esc_attr($_GET['page'] ?? ''); ?>">
            
            <div class="vedmg-filter-group">
                <label for="search">Search Courses:</label>
                <input type="text" name="search" id="search" class="vedmg-filter-input" 
                       value="<?php echo esc_attr($search_filter); ?>" 
                       placeholder="Search by course name, description, or instructor...">
            </div>
            
            <div class="vedmg-filter-group">
                <label for="per_page">Items per page:</label>
                <select name="per_page" id="per_page" class="vedmg-filter-select">
                    <option value="10" <?php selected($per_page, 10); ?>>10</option>
                    <option value="25" <?php selected($per_page, 25); ?>>25</option>
                    <option value="50" <?php selected($per_page, 50); ?>>50</option>
                    <option value="100" <?php selected($per_page, 100); ?>>100</option>
                </select>
            </div>
            
            <div class="vedmg-filter-group">
                <button type="submit" class="vedmg-classroom-btn">Apply Filters</button>
                <a href="<?php echo admin_url('admin.php?page=' . esc_attr($_GET['page'] ?? '')); ?>" 
                   class="vedmg-classroom-btn vedmg-classroom-btn-secondary">Clear Filters</a>
            </div>
        </form>
        
        <!-- Course Summary -->
        <div class="vedmg-enrollment-summary">
            <span>Total: <strong><?php echo $total_count; ?></strong></span>
            <span>Showing: <strong><?php echo $start_item; ?>-<?php echo $end_item; ?></strong></span>
            <span>Page: <strong><?php echo $current_page; ?> of <?php echo $total_pages; ?></strong></span>
        </div>

        <p>Here you can view all courses created by instructors and manage Google Classroom integration.</p>
        
        <!-- Course Management Table -->
        <table class="vedmg-classroom-table">
            <thead>
                <tr>
                    <th>Course Name</th>
                    <th>Instructor</th>
                    <th>Created Date</th>
                    <th>Students Enrolled</th>
                    <th>Classroom Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($courses)): ?>
                    <?php foreach ($courses as $course): ?>
                        <tr class="real-data-row" data-course-id="<?php echo $course->course_id; ?>">
                            <td>
                                <strong><?php echo esc_html($course->course_name); ?></strong>
                                <small style="display: block; color: #666;">(Real Data)</small>
                            </td>
                            <td><?php echo esc_html($course->instructor_name ?: 'Unknown'); ?></td>
                            <td><?php echo VedMG_ClassRoom_Database_Helper::format_date($course->created_date); ?></td>
                            <td>
                                <span class="vedmg-student-count"><?php echo intval($course->student_count); ?></span>
                                <a href="<?php echo admin_url('admin.php?page=vedmg-classroom-enrollments&course_id=' . $course->course_id); ?>" class="vedmg-view-students">
                                    View Students
                                </a>
                            </td>
                            <td>
                                <span class="vedmg-classroom-status" data-status="<?php echo $course->classroom_status; ?>">
                                    <?php echo VedMG_ClassRoom_Database_Helper::format_classroom_status($course->classroom_status); ?>
                                </span>
                            </td>
                            <td>
                                <div class="vedmg-action-buttons">
                                    <?php if ($course->classroom_status === 'pending'): ?>
                                        <button class="vedmg-classroom-btn vedmg-create-classroom-btn" data-course-id="<?php echo $course->course_id; ?>">
                                            Create Classroom
                                        </button>
                                    <?php else: ?>
                                        <button class="vedmg-classroom-btn vedmg-manage-classroom-btn" data-course-id="<?php echo $course->course_id; ?>">
                                            Manage Classroom
                                        </button>
                                    <?php endif; ?>
                                    
                                    <button class="vedmg-classroom-btn vedmg-classroom-btn-info vedmg-view-course-btn" data-course-id="<?php echo $course->course_id; ?>">
                                        View
                                    </button>
                                    
                                    <button class="vedmg-classroom-btn vedmg-classroom-btn-warning vedmg-meeting-link-btn" data-course-id="<?php echo $course->course_id; ?>">
                                        <span class="vedmg-classroom-spinner"></span>
                                        Generate/Update Meeting Link
                                    </button>
                                    
                                    <button class="vedmg-classroom-btn vedmg-classroom-btn-danger vedmg-delete-course-btn" data-course-id="<?php echo $course->course_id; ?>">
                                        Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
                
                <?php if (empty($courses)): ?>
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 20px; color: #666;">
                            <em>No courses found in database. Activate the plugin to create database tables, then add some courses.</em>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <!-- Server-Side Pagination -->
    <?php if ($total_pages > 1): ?>
    <div class="vedmg-pagination">
        <div class="vedmg-pagination-info">
            Showing <?php echo $start_item; ?> to <?php echo $end_item; ?> of <?php echo $total_count; ?> courses
        </div>
        <div class="vedmg-pagination-controls">
            <?php
            $base_url = admin_url('admin.php');
            $query_args = array_merge($_GET, array('page' => $_GET['page']));
            
            // First page
            if ($current_page > 1):
                $first_url = add_query_arg(array_merge($query_args, array('paged' => 1)), $base_url);
            ?>
                <a href="<?php echo esc_url($first_url); ?>" class="vedmg-pagination-btn">‹‹</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">‹‹</span>
            <?php endif; ?>
            
            <?php
            // Previous page
            if ($current_page > 1):
                $prev_url = add_query_arg(array_merge($query_args, array('paged' => $current_page - 1)), $base_url);
            ?>
                <a href="<?php echo esc_url($prev_url); ?>" class="vedmg-pagination-btn">‹</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">‹</span>
            <?php endif; ?>
            
            <div class="vedmg-pagination-numbers">
                <?php
                // Calculate page range
                $start_page = max(1, $current_page - 2);
                $end_page = min($total_pages, $current_page + 2);
                
                for ($i = $start_page; $i <= $end_page; $i++):
                    if ($i === $current_page):
                ?>
                    <span class="vedmg-pagination-btn active"><?php echo $i; ?></span>
                <?php else:
                    $page_url = add_query_arg(array_merge($query_args, array('paged' => $i)), $base_url);
                ?>
                    <a href="<?php echo esc_url($page_url); ?>" class="vedmg-pagination-btn"><?php echo $i; ?></a>
                <?php endif; endfor; ?>
            </div>
            
            <?php
            // Next page
            if ($current_page < $total_pages):
                $next_url = add_query_arg(array_merge($query_args, array('paged' => $current_page + 1)), $base_url);
            ?>
                <a href="<?php echo esc_url($next_url); ?>" class="vedmg-pagination-btn">›</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">›</span>
            <?php endif; ?>
            
            <?php
            // Last page
            if ($current_page < $total_pages):
                $last_url = add_query_arg(array_merge($query_args, array('paged' => $total_pages)), $base_url);
            ?>
                <a href="<?php echo esc_url($last_url); ?>" class="vedmg-pagination-btn">››</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">››</span>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Meeting Link Modal -->
<div id="vedmg-meeting-link-modal" class="vedmg-modal" style="display: none;">
    <div class="vedmg-modal-content">
        <div class="vedmg-modal-header">
            <h3>Generate/Update Meeting Link</h3>
            <span class="vedmg-modal-close">&times;</span>
        </div>
        <div class="vedmg-modal-body">
            <form id="vedmg-meeting-link-form">
                <input type="hidden" id="meeting-course-id" name="course_id" value="">
                
                <div class="vedmg-form-group">
                    <label for="meeting-title">Meeting Title:</label>
                    <input type="text" id="meeting-title" name="meeting_title" class="vedmg-form-control" readonly>
                </div>
                
                <div class="vedmg-form-group">
                    <label for="meeting-description">Meeting Description:</label>
                    <textarea id="meeting-description" name="meeting_description" class="vedmg-form-control" rows="4" readonly></textarea>
                </div>
                
                <div class="vedmg-form-group">
                    <label for="meeting-link">Meeting Link (Manual Entry):</label>
                    <input type="url" id="meeting-link" name="meeting_link" class="vedmg-form-control" placeholder="Enter Google Meet or Zoom link manually" required>
                    <p class="description">Enter the Google Meet, Zoom, or other meeting platform link for this session.</p>
                </div>
                
                <div class="vedmg-form-group">
                    <label for="assigned-teacher">Assign to Teacher:</label>
                    <select id="assigned-teacher" name="assigned_teacher" class="vedmg-form-control" disabled>
                        <option value="">Select a teacher...</option>
                        <!-- Teachers will be loaded dynamically -->
                    </select>
                    <p class="description">Select the teacher who will conduct this meeting session.</p>
                </div>
                
                <div class="vedmg-form-group">
                    <label for="meeting-duration">Duration (minutes):</label>
                    <select id="meeting-duration" name="meeting_duration" class="vedmg-form-control" disabled>
                        <option value="30">30 minutes</option>
                        <option value="45">45 minutes</option>
                        <option value="60" selected>1 hour</option>
                        <option value="90">1.5 hours</option>
                        <option value="120">2 hours</option>
                        <option value="180">3 hours</option>
                    </select>
                </div>
                
                <div class="vedmg-form-group">
                    <label>
                        <input type="checkbox" id="auto-record" name="auto_record" value="1" disabled>
                        Auto-record meeting
                    </label>
                </div>
                
                <div class="vedmg-form-group">
                    <label>
                        <input type="checkbox" id="send-invitations" name="send_invitations" value="1" checked disabled>
                        Send invitations to enrolled students
                    </label>
                </div>
                
                <div class="vedmg-form-actions">
                    <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="cancel-meeting-link">Cancel</button>
                    <button type="submit" class="vedmg-classroom-btn vedmg-classroom-btn-primary" id="save-meeting-link">
                        <span class="vedmg-classroom-spinner"></span>
                        Generate Meeting Link
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* Modal Styles */
.vedmg-modal {
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    overflow-y: auto;
}

.vedmg-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    animation: modalFadeIn 0.3s ease;
    position: relative;
}

/* Management Modal Specific Styles */
.vedmg-management-modal {
    padding: 20px 0;
}

.vedmg-management-modal-content {
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    margin: 2% auto;
}

.vedmg-management-modal-body {
    max-height: 70vh;
    overflow-y: auto;
    padding: 25px;
}

/* Management Grid */
.vedmg-management-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.vedmg-management-card {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.vedmg-management-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-color: #0073aa;
}

.vedmg-management-card h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 16px;
}

.vedmg-management-card p {
    color: #666;
    font-size: 14px;
    margin: 0 0 15px 0;
    line-height: 1.5;
}

.vedmg-management-card .vedmg-classroom-btn {
    width: 100%;
    justify-content: center;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.vedmg-modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 10;
}

.vedmg-modal-header h3 {
    margin: 0;
    color: #333;
}

.vedmg-modal-close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
    padding: 5px;
    line-height: 1;
    border: none;
    background: none;
    outline: none;
}

.vedmg-modal-close:hover {
    color: #000;
}

.vedmg-modal-body {
    padding: 25px;
}

.vedmg-form-group {
    margin-bottom: 20px;
}

.vedmg-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.vedmg-form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.vedmg-form-control:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,115,170,0.1);
}

.vedmg-form-row {
    display: flex;
    gap: 15px;
}

.vedmg-form-half {
    flex: 1;
}

.vedmg-form-actions {
    margin-top: 30px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.vedmg-form-group input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

/* Responsive Design for Modals */
@media (max-width: 768px) {
    .vedmg-modal-content {
        width: 95%;
        margin: 2% auto;
    }
    
    .vedmg-management-modal-content {
        margin: 1% auto;
        max-height: 95vh;
    }
    
    .vedmg-management-modal-body {
        max-height: 80vh;
        padding: 15px;
    }
    
    .vedmg-management-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .vedmg-management-card {
        padding: 15px;
    }
    
    .vedmg-modal-header {
        padding: 15px 20px;
    }
    
    .vedmg-form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .vedmg-form-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .vedmg-modal-content {
        width: 98%;
        margin: 1% auto;
    }
    
    .vedmg-management-modal-content {
        max-height: 98vh;
    }
    
    .vedmg-modal-header h3 {
        font-size: 16px;
    }
    
    .vedmg-management-card h4 {
        font-size: 14px;
    }
    
    .vedmg-management-card p {
        font-size: 13px;
    }
}

/* Google Classroom Controls Styles */
.vedmg-google-classroom-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px solid #0073aa;
}

.vedmg-instructor-selection,
.vedmg-email-override {
    display: flex;
    align-items: center;
    gap: 10px;
}

.vedmg-instructor-selection label,
.vedmg-email-override label {
    font-weight: 600;
    color: #333;
    white-space: nowrap;
}

.vedmg-google-classroom-controls select,
.vedmg-google-classroom-controls input[type="email"] {
    min-width: 250px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.vedmg-google-classroom-controls button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.vedmg-section-header {
    display: flex;
    flex-direction: column;
}

.vedmg-section-header h2 {
    margin-bottom: 10px;
}

/* Loading states */
.vedmg-loading {
    opacity: 0.6;
    pointer-events: none;
}

.vedmg-loading::after {
    content: " Loading...";
    font-style: italic;
}

/* Responsive design for Google Classroom controls */
@media (max-width: 768px) {
    .vedmg-google-classroom-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .vedmg-instructor-selection,
    .vedmg-email-override {
        flex-direction: column;
        align-items: stretch;
    }
    
    .vedmg-google-classroom-controls select,
    .vedmg-google-classroom-controls input[type="email"] {
        min-width: auto;
        width: 100%;
    }
}
</style>

<script>
// Course management specific JavaScript
jQuery(document).ready(function($) {
    console.log('Course management page loaded');
    
    // Load instructors on page load
    loadInstructors();
    
    // Handle instructor dropdown change or manual email input
    $('#instructor-dropdown, #manual-email').on('change input', function() {
        const selectedInstructorId = $('#instructor-dropdown').val();
        const selectedOption = $('#instructor-dropdown option:selected');
        const manualEmail = $('#manual-email').val().trim();
        
        // Instructor selection is MANDATORY - check for instructor ID, not email
        if (!selectedInstructorId || selectedInstructorId === '') {
            $('#sync-google-classroom').prop('disabled', true);
            return;
        }
        
        // If instructor is selected (has valid ID), enable sync button
        // Manual email is optional override
        $('#sync-google-classroom').prop('disabled', false);
        
        // Clear manual email if user changes instructor selection
        if ($(this).attr('id') === 'instructor-dropdown') {
            // Don't clear manual email automatically - let user decide
            console.log('Instructor selected:', {
                id: selectedInstructorId,
                name: selectedOption.data('name'),
                email: selectedOption.data('email')
            });
        }
    });
    
    // Handle Google Classroom sync
    $('#sync-google-classroom').on('click', function() {
        const selectedInstructorId = $('#instructor-dropdown').val();
        const selectedOption = $('#instructor-dropdown option:selected');
        const selectedInstructorName = selectedOption.data('name');
        const selectedInstructorEmail = selectedOption.data('email');
        const manualEmail = $('#manual-email').val().trim();
        
        // Validate: Instructor selection is mandatory (check for valid instructor ID)
        if (!selectedInstructorId || selectedInstructorId === '' || selectedInstructorId === '0') {
            alert('Please select an instructor first. This is required to attribute courses correctly.');
            return;
        }
        
        // Determine email to use: manual override OR instructor's email
        const emailToUse = manualEmail || selectedInstructorEmail;
        
        console.log('Sync starting with:', {
            instructor_id: selectedInstructorId,
            instructor_name: selectedInstructorName,
            instructor_email: selectedInstructorEmail,
            manual_email: manualEmail,
            email_to_use: emailToUse,
            is_email_override: !!manualEmail
        });
        
        syncGoogleClassroom(emailToUse, selectedInstructorName, selectedInstructorId);
    });
    
    /**
     * Validate email format
     */
    function validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    /**
     * Load instructors for dropdown
     */
    function loadInstructors() {
        const $dropdown = $('#instructor-dropdown');
        
        // Show loading state
        $dropdown.html('<option value="">Loading instructors...</option>').prop('disabled', true);
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'vedmg_get_all_instructors',
                nonce: '<?php echo wp_create_nonce('vedmg_classroom_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    populateInstructorDropdown(response.data.instructors);
                } else {
                    console.error('Failed to load instructors:', response.data);
                    $dropdown.html('<option value="">Error loading instructors</option>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error loading instructors:', error);
                $dropdown.html('<option value="">Error loading instructors</option>');
            },
            complete: function() {
                $dropdown.prop('disabled', false);
            }
        });
    }
    
    /**
     * Populate instructor dropdown with data
     */
    function populateInstructorDropdown(instructors) {
        const $dropdown = $('#instructor-dropdown');
        
        // Clear existing options
        $dropdown.empty();
        
        // Add default option
        $dropdown.append('<option value="">-- Select Instructor --</option>');
        
        // Add instructor options
        if (instructors && instructors.length > 0) {
            instructors.forEach(function(instructor) {
                const optionText = `${instructor.instructor_name} (${instructor.instructor_email}) - ${instructor.course_count} courses`;
                $dropdown.append(`<option value="${instructor.instructor_id}" data-email="${instructor.instructor_email}" data-name="${instructor.instructor_name}">${optionText}</option>`);
            });
        } else {
            $dropdown.append('<option value="">No instructors found</option>');
        }
    }
    
    /**
     * Sync Google Classroom for selected instructor
     */
    function syncGoogleClassroom(instructorEmail, instructorName, instructorId) {
        const $button = $('#sync-google-classroom');
        const originalText = $button.text();
        
        // Show loading state
        $button.addClass('vedmg-loading').prop('disabled', true).text('Syncing...');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'vedmg_sync_google_classroom',
                instructor_email: instructorEmail,
                instructor_name: instructorName,
                instructor_id: instructorId,
                nonce: '<?php echo wp_create_nonce('vedmg_classroom_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    let message = `Google Classroom sync completed successfully!\n\n` +
                                `Instructor: ${data.instructor_email}\n` +
                                `Database courses found: ${data.database_courses}\n` +
                                `Google Classroom courses: ${data.google_courses}\n` +
                                `Matches found: ${data.matches_found}\n` +
                                `Courses updated: ${data.courses_updated}\n` +
                                `New courses created: ${data.created || 0}`;
                    
                    if (data.matched_details && data.matched_details.length > 0) {
                        message += `\n\nCourse Actions:\n`;
                        data.matched_details.forEach(function(detail, index) {
                            const actionText = detail.action === 'created' ? 'Created' : 'Updated';
                            message += `${index + 1}. ${actionText}: "${detail.google_course}"`;
                            if (detail.action === 'updated') {
                                message += ` (matched with "${detail.db_course}")`;
                            }
                            message += `\n`;
                        });
                    }
                    
                    alert(message);
                    
                    // Refresh the page to show updated courses
                    location.reload();
                } else {
                    alert('Sync failed: ' + (response.data || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error during sync:', error);
                alert('Sync failed due to network error. Please try again.');
            },
            complete: function() {
                // Restore button state
                $button.removeClass('vedmg-loading').prop('disabled', false).text(originalText);
                
                // Re-enable button if instructor is still selected
                const selectedEmail = $('#instructor-dropdown').val();
                $('#sync-google-classroom').prop('disabled', !selectedEmail);
            }
        });
    }
    
    // Any course-specific JavaScript will be handled by courses.js
});
</script>
