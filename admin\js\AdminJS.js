/**
 * VedMG ClassRoom Admin JavaScript
 * 
 * JavaScript functionality for the admin interface of the VedMG ClassRoom plugin.
 * This file handles all client-side interactions and AJAX requests.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

(function($) {
    'use strict';

    /**
     * Console logging utility
     * Controls whether console messages are displayed based on admin setting
     */
    window.vedmg_console_log = function(message, data) {
        // Check if console logging is enabled from admin settings
        if (typeof vedmg_classroom_ajax !== 'undefined' && vedmg_classroom_ajax.console_logging_enabled === '1') {
            if (data !== undefined) {
                console.log('[VedMG ClassRoom] ' + message, data);
            } else {
                console.log('[VedMG ClassRoom] ' + message);
            }
        }
    };

    /**
     * Console error logging utility
     * Controls whether console errors are displayed based on admin setting
     */
    window.vedmg_console_error = function(message, data) {
        // Check if console logging is enabled from admin settings
        if (typeof vedmg_classroom_ajax !== 'undefined' && vedmg_classroom_ajax.console_logging_enabled === '1') {
            if (data !== undefined) {
                console.error('[VedMG ClassRoom] ' + message, data);
            } else {
                console.error('[VedMG ClassRoom] ' + message);
            }
        }
    };

    /**
     * Console warning logging utility
     * Controls whether console warnings are displayed based on admin setting
     */
    window.vedmg_console_warn = function(message, data) {
        // Check if console logging is enabled from admin settings
        if (typeof vedmg_classroom_ajax !== 'undefined' && vedmg_classroom_ajax.console_logging_enabled === '1') {
            if (data !== undefined) {
                console.warn('[VedMG ClassRoom] ' + message, data);
            } else {
                console.warn('[VedMG ClassRoom] ' + message);
            }
        }
    };

    /**
     * Main admin object
     * Contains all admin functionality
     */
    var VedMGClassRoomAdmin = {
        
        /**
         * Initialize the admin interface
         */
        init: function() {
            vedmg_console_log('VedMG ClassRoom Admin initialized');
            
            // Bind event handlers
            this.bindEvents();
            
            // Initialize components
            this.initComponents();
        },
        
        /**
         * Bind event handlers
         * Attach event listeners to various elements
         */
        bindEvents: function() {
            // Test button click
            $(document).on('click', '.vedmg-test-btn', this.handleTestAction);
            
            // Debug toggle
            $(document).on('change', '#debug_enabled', this.handleDebugToggle);
            
            // Console logging toggle
            $(document).on('change', '#vedmg_classroom_console_logging_enabled', this.handleConsoleLoggingToggle);
            
            // Form submissions
            $(document).on('submit', '.vedmg-classroom-form', this.handleFormSubmit);
            
            // Enrollment actions
            $(document).on('click', '.vedmg-enroll-btn', this.handleEnrollAction);
            
            vedmg_console_log('Event handlers bound');
        },
        
        /**
         * Initialize components
         * Set up various UI components
         */
        initComponents: function() {
            // Initialize any special components here
            vedmg_console_log('Components initialized');
        },
        
        /**
         * Handle test action
         * Performs a test AJAX request
         */
        handleTestAction: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $spinner = $button.find('.vedmg-classroom-spinner');
            
            // Show spinner
            $spinner.show();
            $button.prop('disabled', true);
            
            // Perform AJAX request
            $.ajax({
                url: vedmg_classroom_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'vedmg_classroom_action',
                    action_type: 'test_action',
                    nonce: vedmg_classroom_ajax.nonce
                },
                success: function(response) {
                    vedmg_console_log('Test action successful:', response);
                    alert('Test action completed successfully!');
                },
                error: function(xhr, status, error) {
                    vedmg_console_error('Test action failed:', error);
                    alert('Test action failed. Please try again.');
                },
                complete: function() {
                    // Hide spinner
                    $spinner.hide();
                    $button.prop('disabled', false);
                }
            });
        },
        
        /**
         * Handle debug toggle
         * Toggles debug mode on/off
         */
        handleDebugToggle: function() {
            var isEnabled = $(this).is(':checked');
            vedmg_console_log('Debug toggle changed:', isEnabled);
            
            // You can add additional logic here if needed
            // For example, show/hide debug sections
            if (isEnabled) {
                $('.vedmg-classroom-debug').show();
            } else {
                $('.vedmg-classroom-debug').hide();
            }
        },
        
        /**
         * Handle console logging toggle
         * Toggles console logging on/off
         */
        handleConsoleLoggingToggle: function() {
            var isEnabled = $(this).is(':checked');
            vedmg_console_log('Console logging toggle changed:', isEnabled);
            
            // Show immediate feedback
            if (isEnabled) {
                vedmg_console_log('Console logging is now enabled');
            } else {
                // This will be the last message shown if disabling
                vedmg_console_log('Console logging is now disabled');
            }
        },
        
        /**
         * Handle form submission
         * Processes form submissions with validation
         */
        handleFormSubmit: function(e) {
            // Basic form validation can be added here
            vedmg_console_log('Form submitted');
            
            // Add any client-side validation
            var isValid = VedMGClassRoomAdmin.validateForm($(this));
            
            if (!isValid) {
                e.preventDefault();
                return false;
            }
            
            // Show loading state
            $(this).find('.vedmg-classroom-btn').prop('disabled', true);
        },
        
        /**
         * Validate form
         * Performs client-side form validation
         */
        validateForm: function($form) {
            var isValid = true;
            
            // Add validation logic here
            // For example, check required fields
            $form.find('[required]').each(function() {
                if ($(this).val().trim() === '') {
                    $(this).addClass('error');
                    isValid = false;
                } else {
                    $(this).removeClass('error');
                }
            });
            
            return isValid;
        },
        
        /**
         * Handle enrollment action
         * Processes student enrollment to Google Classroom
         */
        handleEnrollAction: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var studentId = $button.data('student-id');
            var classroomId = $button.closest('tr').find('.classroom-select').val();
            
            // Validate selection
            if (!classroomId) {
                alert('Please select a classroom first.');
                return;
            }
            
            // Confirm action
            if (!confirm('Are you sure you want to enroll this student?')) {
                return;
            }
            
            // Show loading state
            $button.prop('disabled', true);
            $button.text('Enrolling...');
            
            // Perform enrollment (placeholder for now)
            setTimeout(function() {
                alert('Student enrolled successfully! (This is a placeholder)');
                $button.prop('disabled', false);
                $button.text('Enroll');
                
                // Update status in the UI
                $button.closest('tr').find('.enrollment-status').text('Enrolled');
            }, 1000);
            
            vedmg_console_log('Enrollment action:', {
                studentId: studentId,
                classroomId: classroomId
            });
        },
        
        /**
         * Show loading state
         * Displays loading indicators
         */
        showLoading: function($element) {
            $element.addClass('loading');
            $element.find('.vedmg-classroom-spinner').show();
        },
        
        /**
         * Hide loading state
         * Hides loading indicators
         */
        hideLoading: function($element) {
            $element.removeClass('loading');
            $element.find('.vedmg-classroom-spinner').hide();
        },
        
        /**
         * Show message
         * Displays success/error messages
         */
        showMessage: function(message, type) {
            type = type || 'success';
            
            var $message = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
            $('.vedmg-classroom-admin').prepend($message);
            
            // Auto-hide after 5 seconds
            setTimeout(function() {
                $message.fadeOut();
            }, 5000);
        }
    };
    
    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        VedMGClassRoomAdmin.init();
    });
    
    // Make the admin object available globally
    window.VedMGClassRoomAdmin = VedMGClassRoomAdmin;
    
})(jQuery);
