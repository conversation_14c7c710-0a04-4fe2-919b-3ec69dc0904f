/**
 * Ved<PERSON> ClassRoom Courses CSS
 * 
 * Specific styles for the course management page.
 * Contains styles for course tables, action buttons, and course-specific elements.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

/* Section Headers */
.vedmg-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.vedmg-section-header h2 {
    margin: 0;
    padding: 0;
    border: none;
}

.vedmg-section-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Course Management Specific */
.vedmg-view-students {
    display: block;
    font-size: 12px;
    color: #0073aa;
    text-decoration: none;
    margin-top: 4px;
}

.vedmg-view-students:hover {
    text-decoration: underline;
}

.vedmg-student-count {
    font-weight: 600;
    color: #23282d;
}

/* Course Action Buttons */
.vedmg-classroom-btn-info {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: white;
}

.vedmg-classroom-btn-info:hover {
    background-color: #138496;
    border-color: #117a8b;
}

/* Course Details Viewer Modal */
.vedmg-course-details {
    display: grid;
    gap: 15px;
    padding: 10px 0;
}

.vedmg-detail-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.vedmg-detail-group label {
    font-weight: 600;
    color: #23282d;
    font-size: 14px;
}

.vedmg-detail-value {
    padding: 8px 12px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.4;
    min-height: 20px;
}

.vedmg-status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: capitalize;
    width: auto;
    max-width: 120px;
}

.vedmg-status-badge[data-status="active"] {
    background-color: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.vedmg-status-badge[data-status="pending"] {
    background-color: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

/* Form controls in modals */
.vedmg-form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.vedmg-form-group {
    margin-bottom: 15px;
}

.vedmg-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #23282d;
}

.vedmg-form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

.description {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
    font-style: italic;
}

/* Pagination Styles for Courses */
.vedmg-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 25px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.vedmg-pagination-info {
    color: #666;
    font-size: 14px;
}

.vedmg-pagination-controls {
    display: flex;
    align-items: center;
    gap: 5px;
}

.vedmg-pagination-btn {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-size: 14px;
    display: inline-block;
}

.vedmg-pagination-btn:hover:not(.disabled) {
    background: #e9ecef;
    border-color: #adb5bd;
    text-decoration: none;
    color: #495057;
}

.vedmg-pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f8f9fa;
    color: #6c757d;
}

.vedmg-pagination-btn.active {
    background: #0073aa;
    color: white;
    border-color: #0073aa;
}

.vedmg-pagination-numbers {
    display: flex;
    gap: 2px;
}

/* Responsive pagination */
@media (max-width: 768px) {
    .vedmg-pagination {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .vedmg-pagination-controls {
        order: -1;
    }
}

.vedmg-classroom-status[data-status="active"] .vedmg-status-active {
    background: #d4edda;
    color: #155724;
}

.vedmg-classroom-status[data-status="pending"] .vedmg-status-pending {
    background: #fff3cd;
    color: #856404;
}

.vedmg-classroom-status[data-status="inactive"] .vedmg-status-inactive {
    background: #f8d7da;
    color: #721c24;
}

/* Action Buttons */
.vedmg-action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.vedmg-action-buttons .vedmg-classroom-btn {
    padding: 6px 12px;
    font-size: 12px;
    min-width: auto;
    white-space: nowrap;
}

.vedmg-create-classroom-btn {
    background: #28a745;
    border-color: #28a745;
}

.vedmg-create-classroom-btn:hover {
    background: #218838;
    border-color: #1e7e34;
}

.vedmg-manage-classroom-btn {
    background: #17a2b8;
    border-color: #17a2b8;
}

.vedmg-manage-classroom-btn:hover {
    background: #138496;
    border-color: #117a8b;
}

/* Warning and Success Button Styles */
.vedmg-classroom-btn-warning {
    background: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.vedmg-classroom-btn-warning:hover {
    background: #e0a800;
    border-color: #d39e00;
    color: #212529;
}

.vedmg-classroom-btn-success {
    background: #28a745;
    border-color: #28a745;
    color: #fff;
}

.vedmg-classroom-btn-success:hover {
    background: #218838;
    border-color: #1e7e34;
    color: #fff;
}

.vedmg-classroom-btn-primary {
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
}

.vedmg-classroom-btn-primary:hover {
    background: #005a87;
    border-color: #005a87;
    color: #fff;
}

/* Loading spinners */
.vedmg-classroom-spinner {
    display: none;
    width: 14px;
    height: 14px;
    border: 2px solid #fff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: vedmgSpin 1s linear infinite;
    margin-right: 5px;
    vertical-align: middle;
}

.vedmg-classroom-btn .vedmg-classroom-spinner {
    display: inline-block;
    margin-right: 5px;
}

@keyframes vedmgSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Course Statistics */
.vedmg-course-stats {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
    margin-top: 15px;
}

.vedmg-stat-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.vedmg-stat-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.vedmg-stat-value {
    font-size: 20px;
    font-weight: bold;
    color: #0073aa;
}

/* Success Indicator */
.vedmg-success-indicator {
    color: #28a745;
    font-size: 12px;
    font-weight: 500;
    margin-left: 10px;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .vedmg-section-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .vedmg-action-buttons {
        justify-content: flex-start;
    }
    
    .vedmg-action-buttons .vedmg-classroom-btn {
        padding: 8px 12px;
        font-size: 13px;
    }
    
    .vedmg-course-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .vedmg-stat-item {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        background: #f9f9f9;
        border-radius: 4px;
    }
    
    .vedmg-stat-value {
        font-size: 18px;
    }
}

@media (max-width: 480px) {
    .vedmg-section-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .vedmg-section-actions .vedmg-classroom-btn {
        text-align: center;
        margin-bottom: 5px;
    }
    
    .vedmg-action-buttons {
        flex-direction: column;
        gap: 5px;
    }
    
    .vedmg-action-buttons .vedmg-classroom-btn {
        padding: 10px 12px;
        text-align: center;
    }
}
