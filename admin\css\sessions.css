/**
 * Ved<PERSON> ClassRoom Sessions CSS
 * 
 * Specific styles for the class sessions page.
 * Contains styles for session cards, scheduling, and session-specific elements.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

/* Session Cards */
.vedmg-featured-sessions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.vedmg-session-card {
    background: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.vedmg-session-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.vedmg-session-header {
    margin-bottom: 15px;
    border-bottom: 1px solid #e5e5e5;
    padding-bottom: 10px;
}

.vedmg-session-header h4 {
    margin: 0 0 8px 0;
    color: #23282d;
    font-size: 16px;
}

.vedmg-session-time {
    font-size: 13px;
    color: #666;
    font-weight: 500;
}

.vedmg-session-details p {
    margin: 8px 0;
    font-size: 14px;
    color: #555;
}

.vedmg-session-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.vedmg-session-actions .vedmg-classroom-btn {
    font-size: 12px;
    padding: 6px 12px;
}

/* Empty State */
.vedmg-empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.vedmg-empty-state p {
    margin-bottom: 20px;
    font-size: 16px;
}

/* Session Summary */
.vedmg-session-summary {
    display: flex;
    gap: 20px;
    font-size: 14px;
    color: #666;
    flex-wrap: wrap;
}

.vedmg-session-summary strong {
    color: #23282d;
}

/* Session Status */
.vedmg-session-status[data-status="scheduled"] .vedmg-status-pending {
    background: #fff3cd;
    color: #856404;
}

.vedmg-session-status[data-status="active"] .vedmg-status-active {
    background: #d4edda;
    color: #155724;
}

.vedmg-session-status[data-status="completed"] .vedmg-status-active {
    background: #cce5ff;
    color: #004085;
}

.vedmg-session-status[data-status="cancelled"] .vedmg-status-inactive {
    background: #f8d7da;
    color: #721c24;
}

/* Join Meet Button */
.vedmg-join-meet-btn {
    background: #34a853;
    border-color: #34a853;
}

.vedmg-join-meet-btn:hover {
    background: #2e8b47;
    border-color: #2e8b47;
}

/* Cancel Session Button */
.vedmg-cancel-session-btn {
    background: #dc3545;
    border-color: #dc3545;
}

.vedmg-cancel-session-btn:hover {
    background: #c82333;
    border-color: #bd2130;
}

/* Session Statistics */
.vedmg-session-stats {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
    margin-top: 15px;
}

.vedmg-session-stats .vedmg-stat-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.vedmg-session-stats .vedmg-stat-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.vedmg-session-stats .vedmg-stat-value {
    font-size: 20px;
    font-weight: bold;
    color: #0073aa;
}

/* Session Checkboxes */
.session-checkbox {
    margin: 0;
    transform: scale(1.1);
}

#select-all-sessions {
    margin: 0;
    transform: scale(1.1);
}

/* Date Filter Input */
.vedmg-filter-input[type="date"] {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
    background: #fff;
    min-width: 150px;
}

/* View Recording Button */
.vedmg-view-recording-btn {
    background: #6f42c1;
    border-color: #6f42c1;
    font-size: 11px;
    padding: 4px 8px;
}

.vedmg-view-recording-btn:hover {
    background: #5a32a3;
    border-color: #5a32a3;
}

/* Responsive Design */
@media (max-width: 768px) {
    .vedmg-upcoming-sessions {
        grid-template-columns: 1fr;
    }
    
    .vedmg-session-card {
        padding: 15px;
    }
    
    .vedmg-session-actions {
        justify-content: space-between;
    }
    
    .vedmg-session-summary {
        flex-direction: column;
        gap: 8px;
    }
    
    .vedmg-session-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .vedmg-session-stats .vedmg-stat-item {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        background: #f9f9f9;
        border-radius: 4px;
    }
}

@media (max-width: 480px) {
    .vedmg-session-card {
        padding: 12px;
    }
    
    .vedmg-session-header h4 {
        font-size: 14px;
    }
    
    .vedmg-session-actions {
        flex-direction: column;
        gap: 8px;
    }
    
    .vedmg-session-actions .vedmg-classroom-btn {
        text-align: center;
        padding: 8px 12px;
    }
    
    .vedmg-empty-state {
        padding: 30px 15px;
    }
    
    .vedmg-empty-state p {
        font-size: 14px;
    }
}

/* Pagination Styles for Sessions */
.vedmg-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 25px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.vedmg-pagination-info {
    color: #666;
    font-size: 14px;
}

.vedmg-pagination-controls {
    display: flex;
    align-items: center;
    gap: 5px;
}

.vedmg-pagination-btn {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-size: 14px;
    display: inline-block;
}

.vedmg-pagination-btn:hover:not(.disabled) {
    background: #e9ecef;
    border-color: #adb5bd;
    text-decoration: none;
    color: #495057;
}

.vedmg-pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f8f9fa;
    color: #6c757d;
}

.vedmg-pagination-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.vedmg-pagination-numbers {
    display: flex;
    gap: 2px;
}

/* Responsive pagination */
@media (max-width: 768px) {
    .vedmg-pagination {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .vedmg-pagination-controls {
        order: -1;
    }
}

/* Featured Sessions Specific Styles */
.vedmg-feature-session-btn {
    background: #28a745;
    border-color: #28a745;
    color: white;
}

.vedmg-feature-session-btn:hover {
    background: #218838;
    border-color: #1e7e34;
}

.vedmg-unfeature-session-btn,
.vedmg-remove-featured-btn {
    background: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.vedmg-unfeature-session-btn:hover,
.vedmg-remove-featured-btn:hover {
    background: #e0a800;
    border-color: #d39e00;
}

.vedmg-featured-sessions .vedmg-session-card {
    border-left: 4px solid #28a745;
}

.vedmg-featured-sessions .vedmg-session-header h4::before {
    content: "⭐ ";
    color: #ffc107;
}

/* Action buttons spacing */
.vedmg-action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
}

.vedmg-action-buttons .vedmg-classroom-btn {
    white-space: nowrap;
    flex-shrink: 0;
}
