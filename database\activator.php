<?php
/**
 * VedMG ClassRoom Database Activator
 * 
 * This file handles all database table creation when the plugin is activated.
 * It creates only the essential tables for Google Classroom integration.
 * 
 * Tables Created (4 Essential Tables):
 * 1. vedmg_courses - Course details for classroom creation
 * 2. vedmg_student_enrollments - Student purchase to class mapping
 * 3. vedmg_class_sessions - Google Meet session scheduling
 * 4. vedmg_instructor_sync - Instructor sync management and Google integration
 * 
 * Note: API logs use existing debug.log, attendance tracking can be added later
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

/**
 * VedMG ClassRoom Database Activator Class
 * 
 * Handles all database table creation and initial setup
 */
class VedMG_ClassRoom_Database_Activator {
    
    /**
     * Activate the database
     * Creates only the essential 3 tables for the plugin
     */
    public static function activate() {
        // Log activation start
        vedmg_log_info('DATABASE', 'Starting database activation (4 essential tables)');
        
        try {
            // Create only essential database tables
            self::create_courses_table();
            self::create_student_enrollments_table();
            self::create_class_sessions_table();
            self::create_instructor_sync_table();
            
            // Set database version
            self::set_database_version();
            
            // Insert initial data if needed
            self::insert_initial_data();
            
            // Log successful activation
            vedmg_log_info('DATABASE', 'Database activation completed successfully (4 tables created)');
            
        } catch (Exception $e) {
            // Log activation error
            vedmg_log_error('DATABASE', 'Database activation failed', $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Create courses table
     * Stores course details for Google Classroom creation
     */
    private static function create_courses_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'vedmg_courses';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            course_id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            masterstudy_course_id bigint(20) unsigned NOT NULL,
            course_name varchar(255) NOT NULL,
            course_description text,
            instructor_id bigint(20) unsigned NOT NULL,
            instructor_name varchar(255) DEFAULT NULL,
            class_join_link text DEFAULT NULL,
            google_classroom_id varchar(255) DEFAULT NULL,
            classroom_status enum('pending','created','active','archived') DEFAULT 'pending',
            auto_enroll_enabled tinyint(1) DEFAULT 1,
            created_date datetime DEFAULT CURRENT_TIMESTAMP,
            updated_date datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (course_id),
            UNIQUE KEY masterstudy_course_id (masterstudy_course_id),
            UNIQUE KEY google_classroom_id (google_classroom_id),
            KEY instructor_id (instructor_id),
            KEY classroom_status (classroom_status)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // Log table creation
        vedmg_log_database('CREATE', 'vedmg_courses', 'Course details table created');
    }
    
    /**
     * Create student enrollments table
     * Stores student purchase to class mapping
     */
    private static function create_student_enrollments_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'vedmg_student_enrollments';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            enrollment_id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            student_id bigint(20) unsigned NOT NULL,
            student_name varchar(255) NOT NULL,
            student_email varchar(255) NOT NULL,
            student_phone varchar(20) DEFAULT NULL,
            course_id bigint(20) unsigned NOT NULL,
            woocommerce_order_id bigint(20) unsigned NOT NULL,
            google_classroom_id varchar(255) DEFAULT NULL,
            enrollment_status enum('pending','enrolled','completed','dropped','failed') DEFAULT 'pending',
            purchase_date datetime NOT NULL,
            enrollment_date datetime DEFAULT NULL,
            completion_date datetime DEFAULT NULL,
            created_date datetime DEFAULT CURRENT_TIMESTAMP,
            updated_date datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (enrollment_id),
            UNIQUE KEY student_course_unique (student_id, course_id),
            KEY course_id (course_id),
            KEY woocommerce_order_id (woocommerce_order_id),
            KEY google_classroom_id (google_classroom_id),
            KEY enrollment_status (enrollment_status),
            KEY purchase_date (purchase_date),
            KEY student_email (student_email)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // Log table creation
        vedmg_log_database('CREATE', 'vedmg_student_enrollments', 'Student enrollments table created');
    }
    
    /**
     * Create class sessions table
     * Stores Google Meet session scheduling information
     */
    private static function create_class_sessions_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'vedmg_class_sessions';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            session_id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            course_id bigint(20) unsigned NOT NULL,
            google_classroom_id varchar(255) NOT NULL,
            session_title varchar(255) NOT NULL,
            session_description text,
            google_meet_link varchar(500) DEFAULT NULL,
            scheduled_date date NOT NULL,
            start_time time NOT NULL,
            end_time time NOT NULL,
            session_status enum('scheduled','ongoing','completed','cancelled') DEFAULT 'scheduled',
            attendance_required tinyint(1) DEFAULT 1,
            max_participants int(11) DEFAULT NULL,
            created_date datetime DEFAULT CURRENT_TIMESTAMP,
            updated_date datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (session_id),
            KEY course_id (course_id),
            KEY google_classroom_id (google_classroom_id),
            KEY scheduled_date (scheduled_date),
            KEY session_status (session_status),
            KEY start_time (start_time)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // Log table creation
        vedmg_log_database('CREATE', 'vedmg_class_sessions', 'Class sessions table created');
    }
    
    /**
     * Create instructor sync table
     * Stores instructor information for sync management
     */
    private static function create_instructor_sync_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'vedmg_instructor_sync';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            instructor_sync_id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            wordpress_user_id bigint(20) unsigned NOT NULL,
            instructor_name varchar(255) NOT NULL,
            instructor_email varchar(255) NOT NULL,
            instructor_phone varchar(20) DEFAULT NULL,
            sync_status enum('pending','synced','failed','disabled') DEFAULT 'pending',
            google_sync_status enum('not_synced','syncing','synced','failed') DEFAULT 'not_synced',
            last_synced_date datetime DEFAULT NULL,
            last_google_sync_date datetime DEFAULT NULL,
            google_classroom_count int(11) DEFAULT 0,
            sync_error_message text DEFAULT NULL,
            created_date datetime DEFAULT CURRENT_TIMESTAMP,
            updated_date datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (instructor_sync_id),
            UNIQUE KEY wordpress_user_id (wordpress_user_id),
            UNIQUE KEY instructor_email (instructor_email),
            KEY sync_status (sync_status),
            KEY google_sync_status (google_sync_status),
            KEY last_synced_date (last_synced_date)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // Log table creation
        vedmg_log_database('CREATE', 'vedmg_instructor_sync', 'Instructor sync table created');
    }
    
    
    /**
     * Set database version
     * Stores the current database version for future migrations
     */
    private static function set_database_version() {
        // Set database version option
        update_option('vedmg_classroom_db_version', '1.0');
        update_option('vedmg_classroom_db_created_date', current_time('mysql'));
        
        // Log version setting
        vedmg_log_database('UPDATE', 'wp_options', 'Database version set to 1.0');
    }
    
    /**
     * Insert initial data
     * Adds any required initial data to the tables
     */
    private static function insert_initial_data() {
        // For now, we don't need any initial data
        // This function is here for future use
        vedmg_log_info('DATABASE', 'No initial data required - tables are ready');
    }
    
    /**
     * Check if tables exist
     * Verifies that all required tables have been created
     * 
     * @return bool True if all tables exist, false otherwise
     */
    public static function verify_tables_exist() {
        global $wpdb;
        
        // Only check the 4 essential tables
        $tables_to_check = array(
            'vedmg_courses',
            'vedmg_student_enrollments',
            'vedmg_class_sessions',
            'vedmg_instructor_sync'
        );
        
        foreach ($tables_to_check as $table) {
            $table_name = $wpdb->prefix . $table;
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
            
            if ($table_exists !== $table_name) {
                vedmg_log_error('DATABASE', "Table verification failed", "Table $table_name does not exist");
                return false;
            }
        }
        
        vedmg_log_info('DATABASE', 'All 4 essential database tables verified successfully');
        return true;
    }
    
    /**
     * Get database statistics
     * Returns information about the created tables
     * 
     * @return array Database statistics
     */
    public static function get_database_stats() {
        global $wpdb;
        
        $stats = array();
        
        // Only get stats for the 4 essential tables
        $tables = array(
            'vedmg_courses',
            'vedmg_student_enrollments',
            'vedmg_class_sessions',
            'vedmg_instructor_sync'
        );
        
        foreach ($tables as $table) {
            $table_name = $wpdb->prefix . $table;
            $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
            $stats[$table] = intval($count);
        }
        
        return $stats;
    }
}

?>
