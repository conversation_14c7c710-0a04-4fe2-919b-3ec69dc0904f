# VedMG Classroom Integration - API Documentation

This document explains the steps and APIs required to integrate Google Classroom with VedMG. It is intended for developers and non-technical users to understand the workflow and how data is synced.

---

## Features & Workflow

1. **Instructor List Page**
  - Displays a list of all instructors.

2. **Google Classroom Sync**
  - Each instructor has a "Sync" button.
  - Clicking "Sync" synchronizes their Google Classroom classes with VedMG.

3. **Last Updated**
  - Shows the last time data was updated.

4. **Courses Page**
  - After syncing, displays all relevant information about the instructor's classes.

---

## API Integration

### 1. Test API

Test the connection and setup.

- **Method:** `POST`
- **URL:**  
  `https://gclassroom-839391304260.us-central1.run.app/greet`
- **Headers:** None
- **Body (JSON):**
  ```json
  {
  "name": "<PERSON><PERSON><PERSON>"
  }
  ```
- **Sample Response:**
  ```json
  {
  "message": "Hello, <PERSON><PERSON><PERSON>! Welcome to the Flask API."
  }
  ```

---

### 2. List Courses API

Fetch all courses for a given instructor from Google Classroom.

- **Method:** `POST`
- **URL:**  
  `https://gclassroom-839391304260.us-central1.run.app/list_courses`
- **Headers:**
  ```
  x-api-key: G$$gle@VedMG!@#
  ```
- **Body (JSON):**
  ```json
  {
  "instructor_email": "<EMAIL>"
  }
  ```
- **Sample Response:**
  ```json
  [
  {
    "id": "790727019326",
    "name": "Physics 101",
    "section": "Period 1",
    "descriptionHeading": "Introduction to Physics",
    "room": "Room 42",
    "ownerId": "102107884243475682690",
    "creationTime": "2025-08-01T16:09:00.117Z",
    "updateTime": "2025-08-01T16:09:00.117Z",
    "enrollmentCode": "2v5k6yis",
    "courseState": "PROVISIONED",
    "alternateLink": "https://classroom.google.com/c/NzkwNzI3MDE5MzI2",
    "teacherGroupEmail": "<EMAIL>",
    "courseGroupEmail": "<EMAIL>",
    "teacherFolder": {
    "id": "1l0AJoVuDYOiMxrLJ0IMZQ7z-HXUpj3kI4l9e2Kz-ShxCPlEgekeJ7-E4Ip1L7pZ945U0CFHE",
    "title": "Physics 101 Period 1",
    "alternateLink": "https://drive.google.com/drive/folders/1l0AJoVuDYOiMxrLJ0IMZQ7z-HXUpj3kI4l9e2Kz-ShxCPlEgekeJ7-E4Ip1L7pZ945U0CFHE"
    },
    "guardiansEnabled": false,
    "gradebookSettings": {
    "calculationType": "TOTAL_POINTS",
    "displaySetting": "HIDE_OVERALL_GRADE"
    }
  }
  // ... more courses
  ]
  ```

---

### 3. Create Class API

Create a new class in Google Classroom.

- **Method:** `POST`
- **URL:**  
  `https://gclassroom-839391304260.us-central1.run.app/create_class`
- **Headers:**
  ```
  x-api-key: G$$gle@VedMG!@#
  ```
- **Body (JSON):**
  ```json
  {
  "name": "Cloud Engineering",
  "batch": "Batch1",
  "descriptionHeading": "Cloud Engineering Course",
  "location": "GNoida_offline",
  "instructor_email": "<EMAIL>"
  }
  ```
- **Sample Response:**
  ```json
  {
  "id": "798564620256",
  "name": "Cloud Engineering1",
  "section": "Batch1",
  "descriptionHeading": "Cloud Engineering Course1",
  "room": "GNoida_offline",
  "ownerId": "108627282972434607666",
  "creationTime": "2025-08-15T09:52:11.079Z",
  "updateTime": "2025-08-15T09:52:11.079Z",
  "enrollmentCode": "um3rp34t",
  "courseState": "PROVISIONED",
  "alternateLink": "https://classroom.google.com/c/Nzk4NTY0NjIwMjU2",
  "teacherGroupEmail": "<EMAIL>",
  "courseGroupEmail": "<EMAIL>",
  "teacherFolder": {
    "id": "1os7V_DY5nBTGLFfCNDBweZsQBpiwevKdfSxVlGQdqyvDksTheZaO7j6g0MhV3H0XMrQGw0Tw"
  },
  "guardiansEnabled": false,
  "gradebookSettings": {
    "calculationType": "TOTAL_POINTS",
    "displaySetting": "HIDE_OVERALL_GRADE"
  }
  }
  ```

---

### 4. Archive Class API

Archive an existing class in Google Classroom.

- **Method:** `POST`
- **URL:**  
  `https://gclassroom-839391304260.us-central1.run.app/archieve_course`
- **Headers:**
  ```
  x-api-key: G$$gle@VedMG!@#
  ```
- **Body (JSON):**
  ```json
  {
  "course_id": "798564620256",
  "instructor_email": "<EMAIL>"
  }
  ```
- **Sample Response:**
  ```json
  {
  "message": "Class Archived : Cloud Engineering1 (ID: 798564620256)"
  }
  ```

---

### 5. Delete Class API

Delete a class from Google Classroom.

- **Method:** `POST`
- **URL:**  
  `https://gclassroom-839391304260.us-central1.run.app/delete_course`
- **Headers:**
  ```
  x-api-key: G$$gle@VedMG!@#
  ```
- **Body (JSON):**
  ```json
  {
  "course_id": "798564620256",
  "instructor_email": "<EMAIL>"
  }
  ```
- **Sample Response:**
  ```json
  {
  "message": "Deleted course: (ID: 798564620256)"
  }
  ```

---

## Summary

- **Purpose:** Sync instructor and course data between VedMG and Google Classroom.
- **How it works:** Instructors are listed, and their classes can be synced with a button. Data is fetched using secure APIs and displayed on the courses page.
- **For Developers:** Use the provided API endpoints and sample requests to integrate and test the functionality.
