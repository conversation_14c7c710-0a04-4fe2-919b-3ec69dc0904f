<?php
/**
 * VedMG ClassRoom Student Enrollments Page
 * 
 * This page handles student enrollment management functionality.
 * Allows viewing enrollments and assigning students to Google Classroom classes.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

// Include database helper
require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'database/helper.php';

// Log page access
vedmg_log_admin_action('Viewed student enrollments page');

// Handle pagination and filtering parameters
$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$per_page = isset($_GET['per_page']) ? max(10, min(100, intval($_GET['per_page']))) : 10;
$course_filter = isset($_GET['course_id']) ? intval($_GET['course_id']) : '';
$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
$classroom_filter = isset($_GET['classroom_id']) ? sanitize_text_field($_GET['classroom_id']) : '';

// Get paginated data from database
$enrollment_data = VedMG_ClassRoom_Database_Helper::get_student_enrollments($current_page, $per_page, $course_filter, $status_filter, $classroom_filter);
$enrollments = $enrollment_data['enrollments'];
$total_count = $enrollment_data['total_count'];
$total_pages = $enrollment_data['total_pages'];

// Get classroom options for dropdowns
$classroom_options = VedMG_ClassRoom_Database_Helper::get_classroom_options();

// Calculate pagination info
$start_item = (($current_page - 1) * $per_page) + 1;
$end_item = min($current_page * $per_page, $total_count);

// Log filtering if active
if ($course_filter || $status_filter || $classroom_filter) {
    vedmg_log_info('ADMIN', 'Filtering enrollments - Course: ' . $course_filter . ', Status: ' . $status_filter . ', Classroom: ' . $classroom_filter);
}
?>

<div class="vedmg-classroom-admin">
    <!-- Page Header -->
    <div class="vedmg-classroom-header">
        <h1>Student Enrollments</h1>
        <p>Manage student enrollments and assign them to Google Classroom classes</p>
    </div>
    
    <!-- Filter Section -->
    <div class="vedmg-classroom-section">
        <div class="vedmg-section-header">
            <h2>Enrollment Filters</h2>
            <div class="vedmg-section-actions">
                <button class="vedmg-classroom-btn" id="refresh-enrollments">
                    Refresh Enrollments
                </button>
                <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="sync-woocommerce">
                    Sync with WooCommerce
                </button>
                <button class="vedmg-classroom-btn vedmg-classroom-btn-primary" id="fetch-classroom-data">
                    Fetch Classroom Data
                </button>
            </div>
        </div>
        
        <form method="GET" class="vedmg-filter-controls">
            <input type="hidden" name="page" value="<?php echo esc_attr($_GET['page'] ?? ''); ?>">
            
            <div class="vedmg-filter-group">
                <label for="course_id">Filter by Course:</label>
                <select name="course_id" id="course_id" class="vedmg-filter-select">
                    <option value="">All Courses</option>
                    <?php foreach ($classroom_options as $classroom): ?>
                        <option value="<?php echo $classroom->course_id; ?>" 
                                <?php selected($course_filter, $classroom->course_id); ?>>
                            <?php echo esc_html($classroom->course_name); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="vedmg-filter-group">
                <label for="classroom_id">Filter by Classroom:</label>
                <select name="classroom_id" id="classroom_id" class="vedmg-filter-select">
                    <option value="">All Classrooms</option>
                    <?php foreach ($classroom_options as $classroom): ?>
                        <?php if (!empty($classroom->google_classroom_id)): ?>
                        <option value="<?php echo esc_attr($classroom->google_classroom_id); ?>" 
                                <?php selected($classroom_filter, $classroom->google_classroom_id); ?>>
                            <?php echo esc_html($classroom->course_name . ' (ID: ' . $classroom->google_classroom_id . ')'); ?>
                        </option>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="vedmg-filter-group">
                <label for="status">Filter by Status:</label>
                <select name="status" id="status" class="vedmg-filter-select">
                    <option value="">All Statuses</option>
                    <option value="pending" <?php selected($status_filter, 'pending'); ?>>Pending</option>
                    <option value="enrolled" <?php selected($status_filter, 'enrolled'); ?>>Enrolled</option>
                    <option value="active" <?php selected($status_filter, 'active'); ?>>Active</option>
                    <option value="inactive" <?php selected($status_filter, 'inactive'); ?>>Inactive</option>
                </select>
            </div>
            
            <div class="vedmg-filter-group">
                <label for="per_page">Items per page:</label>
                <select name="per_page" id="per_page" class="vedmg-filter-select">
                    <option value="10" <?php selected($per_page, 10); ?>>10</option>
                    <option value="25" <?php selected($per_page, 25); ?>>25</option>
                    <option value="50" <?php selected($per_page, 50); ?>>50</option>
                    <option value="100" <?php selected($per_page, 100); ?>>100</option>
                </select>
            </div>
            
            <div class="vedmg-filter-group">
                <button type="submit" class="vedmg-classroom-btn">Apply Filters</button>
                <a href="<?php echo admin_url('admin.php?page=' . esc_attr($_GET['page'] ?? '')); ?>" 
                   class="vedmg-classroom-btn vedmg-classroom-btn-secondary">Clear</a>
            </div>
        </form>
    </div>
    
    <!-- Student Enrollment Section -->
    <div class="vedmg-classroom-section">
        <div class="vedmg-section-header">
            <h2>Student Enrollments</h2>
            <div class="vedmg-enrollment-summary">
                <span>Total: <strong><?php echo $total_count; ?></strong></span>
                <span>Showing: <strong><?php echo $start_item; ?>-<?php echo $end_item; ?></strong></span>
                <span>Page: <strong><?php echo $current_page; ?> of <?php echo $total_pages; ?></strong></span>
            </div>
        </div>
        
        <!-- Bulk Actions -->
        <div class="vedmg-bulk-actions">
            <select id="bulk-action-select">
                <option value="">Bulk Actions</option>
                <option value="enroll">Enroll Selected</option>
                <option value="unenroll">Unenroll Selected</option>
                <option value="delete">Delete Selected</option>
            </select>
            <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="apply-bulk-action">Apply</button>
            <button class="vedmg-classroom-btn vedmg-classroom-btn-primary" id="group-selected-students" style="display: none;">
                Group Selected Students
            </button>
        </div>
        
        <!-- Student Enrollment Table -->
        <table class="vedmg-classroom-table">
            <thead>
                <tr>
                    <th><input type="checkbox" id="select-all-enrollments"></th>
                    <th>Student Name</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Course</th>
                    <th>Google Classroom</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($enrollments)): ?>
                    <?php foreach ($enrollments as $enrollment): ?>
                        <tr class="enrollment-row" data-enrollment-id="<?php echo $enrollment->enrollment_id; ?>" data-student-id="<?php echo $enrollment->student_id; ?>">
                            <td><input type="checkbox" class="enrollment-checkbox" value="<?php echo $enrollment->enrollment_id; ?>"></td>
                            <td>
                                <strong><?php echo esc_html($enrollment->student_name ?: 'Unknown Student'); ?></strong>
                                <?php if (isset($enrollment->user_status) && $enrollment->user_status === 'missing_user'): ?>
                                    <small style="display: block; color: #d63638; font-style: italic;">(User account missing)</small>
                                <?php endif; ?>
                            </td>
                            <td><?php echo esc_html($enrollment->student_email ?: 'No email'); ?></td>
                            <td><?php echo esc_html($enrollment->student_phone ?: 'No phone'); ?></td>
                            <td><?php echo esc_html($enrollment->course_name ?: 'Unknown Course'); ?></td>
                            <td>
                                <?php if (!empty($enrollment->google_classroom_id)): ?>
                                    <?php
                                    // Find classroom name from the options
                                    $classroom_name = 'Unknown Classroom';
                                    foreach ($classroom_options as $classroom) {
                                        if ($classroom->google_classroom_id == $enrollment->google_classroom_id) {
                                            $classroom_name = $classroom->course_name;
                                            break;
                                        }
                                    }
                                    ?>
                                    <div class="vedmg-classroom-assigned">
                                        <span class="vedmg-classroom-name"><?php echo esc_html($classroom_name); ?></span>
                                        <small class="vedmg-classroom-id">ID: <?php echo esc_html($enrollment->google_classroom_id); ?></small>
                                    </div>
                                <?php else: ?>
                                    <select class="classroom-select" data-enrollment-id="<?php echo $enrollment->enrollment_id; ?>">
                                        <option value="">Select Classroom</option>
                                        <?php foreach ($classroom_options as $classroom): ?>
                                            <option value="<?php echo $classroom->course_id; ?>">
                                                <?php echo esc_html($classroom->course_name); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="vedmg-enrollment-status" data-status="<?php echo $enrollment->enrollment_status; ?>">
                                    <?php echo VedMG_ClassRoom_Database_Helper::format_enrollment_status($enrollment->enrollment_status); ?>
                                </span>
                            </td>
                            <td>
                                <div class="vedmg-action-buttons">
                                    <button class="vedmg-classroom-btn vedmg-enroll-btn" 
                                            data-student-id="<?php echo $enrollment->student_id; ?>" 
                                            data-enrollment-id="<?php echo $enrollment->enrollment_id; ?>">
                                        <?php echo ($enrollment->enrollment_status === 'enrolled') ? 'Update' : 'Enroll'; ?>
                                    </button>
                                    
                                    <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary vedmg-view-student-btn" 
                                            data-student-id="<?php echo $enrollment->student_id; ?>">
                                        View Details
                                    </button>
                                    
                                    <button class="vedmg-classroom-btn vedmg-classroom-btn-accent vedmg-schedule-lab-btn" 
                                            data-student-id="<?php echo $enrollment->student_id; ?>" 
                                            data-enrollment-id="<?php echo $enrollment->enrollment_id; ?>"
                                            data-course-id="<?php echo $enrollment->course_id; ?>">
                                        Schedule Lab
                                    </button>
                                    
                                    <?php 
                                    // Check if student has scheduled sessions
                                    $has_sessions = VedMG_ClassRoom_Database_Helper::has_scheduled_sessions($enrollment->student_id);
                                    $scheduled_btn_class = $has_sessions 
                                        ? 'vedmg-classroom-btn vedmg-classroom-btn-success vedmg-scheduled-btn' 
                                        : 'vedmg-classroom-btn vedmg-classroom-btn-danger vedmg-scheduled-btn-inactive';
                                    $scheduled_btn_text = $has_sessions ? 'Scheduled' : 'Not Scheduled';
                                    ?>
                                    <button class="<?php echo $scheduled_btn_class; ?>" 
                                            data-student-id="<?php echo $enrollment->student_id; ?>"
                                            data-has-sessions="<?php echo $has_sessions ? '1' : '0'; ?>">
                                        <?php echo $scheduled_btn_text; ?>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 20px; color: #666;">
                            <?php if ($course_filter || $status_filter): ?>
                                <em>No enrollments found matching the current filters.</em>
                                <br><a href="<?php echo admin_url('admin.php?page=' . esc_attr($_GET['page'] ?? '')); ?>">Clear filters</a>
                            <?php else: ?>
                                <em>No student enrollments found in database. Students will appear here after purchasing courses.</em>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <!-- Server-Side Pagination -->
    <?php if ($total_pages > 1): ?>
    <div class="vedmg-pagination">
        <div class="vedmg-pagination-info">
            Showing <?php echo $start_item; ?> to <?php echo $end_item; ?> of <?php echo $total_count; ?> enrollments
        </div>
        <div class="vedmg-pagination-controls">
            <?php
            $base_url = admin_url('admin.php');
            $query_args = array_merge($_GET, array('page' => $_GET['page']));
            
            // First page
            if ($current_page > 1):
                $first_url = add_query_arg(array_merge($query_args, array('paged' => 1)), $base_url);
            ?>
                <a href="<?php echo esc_url($first_url); ?>" class="vedmg-pagination-btn">‹‹</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">‹‹</span>
            <?php endif; ?>
            
            <?php
            // Previous page
            if ($current_page > 1):
                $prev_url = add_query_arg(array_merge($query_args, array('paged' => $current_page - 1)), $base_url);
            ?>
                <a href="<?php echo esc_url($prev_url); ?>" class="vedmg-pagination-btn">‹</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">‹</span>
            <?php endif; ?>
            
            <div class="vedmg-pagination-numbers">
                <?php
                // Calculate page range
                $start_page = max(1, $current_page - 2);
                $end_page = min($total_pages, $current_page + 2);
                
                for ($i = $start_page; $i <= $end_page; $i++):
                    if ($i === $current_page):
                ?>
                    <span class="vedmg-pagination-btn active"><?php echo $i; ?></span>
                <?php else:
                    $page_url = add_query_arg(array_merge($query_args, array('paged' => $i)), $base_url);
                ?>
                    <a href="<?php echo esc_url($page_url); ?>" class="vedmg-pagination-btn"><?php echo $i; ?></a>
                <?php endif; endfor; ?>
            </div>
            
            <?php
            // Next page
            if ($current_page < $total_pages):
                $next_url = add_query_arg(array_merge($query_args, array('paged' => $current_page + 1)), $base_url);
            ?>
                <a href="<?php echo esc_url($next_url); ?>" class="vedmg-pagination-btn">›</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">›</span>
            <?php endif; ?>
            
            <?php
            // Last page
            if ($current_page < $total_pages):
                $last_url = add_query_arg(array_merge($query_args, array('paged' => $total_pages)), $base_url);
            ?>
                <a href="<?php echo esc_url($last_url); ?>" class="vedmg-pagination-btn">››</a>
            <?php else: ?>
                <span class="vedmg-pagination-btn disabled">››</span>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Enrollment Edit Modal -->
<div id="vedmg-enrollment-edit-modal" class="vedmg-modal" style="display: none;">
    <div class="vedmg-modal-content">
        <div class="vedmg-modal-header">
            <h3>Edit Student Enrollment</h3>
            <span class="vedmg-modal-close">&times;</span>
        </div>
        <div class="vedmg-modal-body">
            <form id="vedmg-enrollment-edit-form">
                <input type="hidden" id="edit-enrollment-id" name="enrollment_id" value="">
                
                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-student-name">Student Name:</label>
                        <input type="text" id="edit-student-name" name="student_name" class="vedmg-form-control" readonly>
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-student-email">Student Email:</label>
                        <input type="email" id="edit-student-email" name="student_email" class="vedmg-form-control" readonly>
                    </div>
                </div>
                
                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-enrollment-course">Course:</label>
                        <select id="edit-enrollment-course" name="course_id" class="vedmg-form-control" required>
                            <option value="">Select Course</option>
                            <?php foreach ($classroom_options as $classroom): ?>
                                <option value="<?php echo $classroom->course_id; ?>">
                                    <?php echo esc_html($classroom->course_name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-enrollment-status">Enrollment Status:</label>
                        <select id="edit-enrollment-status" name="enrollment_status" class="vedmg-form-control">
                            <option value="active">Active</option>
                            <option value="pending">Pending</option>
                            <option value="completed">Completed</option>
                            <option value="dropped">Dropped</option>
                        </select>
                    </div>
                </div>
                
                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-enrollment-date">Enrollment Date:</label>
                        <input type="date" id="edit-enrollment-date" name="enrollment_date" class="vedmg-form-control" required>
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-classroom-status">Classroom Status:</label>
                        <select id="edit-classroom-status" name="classroom_status" class="vedmg-form-control">
                            <option value="not_invited">Not Invited</option>
                            <option value="invited">Invited</option>
                            <option value="joined">Joined</option>
                            <option value="removed">Removed</option>
                        </select>
                    </div>
                </div>
                
                <div class="vedmg-form-group">
                    <label for="edit-enrollment-notes">Notes:</label>
                    <textarea id="edit-enrollment-notes" name="notes" class="vedmg-form-control" rows="3" placeholder="Additional notes about this enrollment..."></textarea>
                </div>
                
                <div class="vedmg-form-group">
                    <label>Actions:</label>
                    <div class="vedmg-checkbox-group">
                        <label>
                            <input type="checkbox" id="edit-send-classroom-invite" name="send_classroom_invite" value="1">
                            Send Google Classroom invitation
                        </label>
                        <label>
                            <input type="checkbox" id="edit-send-welcome-email" name="send_welcome_email" value="1">
                            Send welcome email to student
                        </label>
                    </div>
                </div>
                
                <div class="vedmg-form-actions">
                    <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="cancel-enrollment-edit">Cancel</button>
                    <button type="submit" class="vedmg-classroom-btn vedmg-classroom-btn-primary" id="save-enrollment-edit">
                        <span class="vedmg-classroom-spinner"></span>
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Student Details Modal -->
<div id="vedmg-student-details-modal" class="vedmg-modal" style="display: none;">
    <div class="vedmg-modal-content">
        <div class="vedmg-modal-header">
            <h3>Student Details</h3>
            <span class="vedmg-modal-close">&times;</span>
        </div>
        <div class="vedmg-modal-body">
            <div class="vedmg-student-details">
                <div class="vedmg-student-profile">
                    <div class="vedmg-profile-avatar">
                        <div class="vedmg-avatar-placeholder"></div>
                    </div>
                    <div class="vedmg-profile-info">
                        <h4 id="details-student-name">--</h4>
                        <p id="details-student-email">--</p>
                        <span class="vedmg-enrollment-status" id="details-student-status">--</span>
                    </div>
                </div>
                
                <div class="vedmg-details-grid">
                    <div class="vedmg-detail-item">
                        <label>Student ID:</label>
                        <span id="details-student-id">--</span>
                    </div>
                    <div class="vedmg-detail-item">
                        <label>Total Enrollments:</label>
                        <span id="details-student-enrollments">--</span>
                    </div>
                    <div class="vedmg-detail-item">
                        <label>Active Courses:</label>
                        <span id="details-student-active-courses">--</span>
                    </div>
                    <div class="vedmg-detail-item">
                        <label>Join Date:</label>
                        <span id="details-student-join-date">--</span>
                    </div>
                    <div class="vedmg-detail-item">
                        <label>Last Activity:</label>
                        <span id="details-student-last-activity">--</span>
                    </div>
                    <div class="vedmg-detail-item">
                        <label>Completion Rate:</label>
                        <span id="details-student-completion-rate">--</span>
                    </div>
                </div>
                
                <div class="vedmg-student-enrollments-list">
                    <h4>Current Enrollments</h4>
                    <div id="details-student-enrollment-list">
                        <p>Loading enrollments...</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="vedmg-modal-footer">
            <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary">Send Message</button>
        </div>
    </div>
</div>

<!-- Enrollment Edit Modal -->
<div id="vedmg-enrollment-edit-modal" class="vedmg-modal" style="display: none;">
    <div class="vedmg-modal-content">
        <div class="vedmg-modal-header">
            <h3>Edit Student Enrollment</h3>
            <span class="vedmg-modal-close">&times;</span>
        </div>
        <div class="vedmg-modal-body">
            <form id="vedmg-enrollment-edit-form">
                <input type="hidden" id="edit-enrollment-id" name="enrollment_id" value="">
                
                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-student-name">Student Name:</label>
                        <input type="text" id="edit-student-name" name="student_name" class="vedmg-form-control" readonly>
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-student-email">Student Email:</label>
                        <input type="email" id="edit-student-email" name="student_email" class="vedmg-form-control" readonly>
                    </div>
                </div>
                
                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-enrollment-course">Course:</label>
                        <select id="edit-enrollment-course" name="course_id" class="vedmg-form-control" required>
                            <option value="">Select Course</option>
                            <?php foreach ($classroom_options as $classroom): ?>
                                <option value="<?php echo $classroom->course_id; ?>">
                                    <?php echo esc_html($classroom->course_name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-enrollment-status">Enrollment Status:</label>
                        <select id="edit-enrollment-status" name="enrollment_status" class="vedmg-form-control">
                            <option value="active">Active</option>
                            <option value="pending">Pending</option>
                            <option value="completed">Completed</option>
                            <option value="dropped">Dropped</option>
                        </select>
                    </div>
                </div>
                
                <div class="vedmg-form-actions">
                    <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="cancel-enrollment-edit">Cancel</button>
                    <button type="submit" class="vedmg-classroom-btn vedmg-classroom-btn-primary" id="save-enrollment-edit">
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Schedule Lab Modal -->
<div id="vedmg-schedule-lab-modal" class="vedmg-modal" style="display: none;">
    <div class="vedmg-modal-content">
        <div class="vedmg-modal-header">
            <h3>Schedule Lab Session</h3>
            <span class="vedmg-modal-close">&times;</span>
        </div>
        <div class="vedmg-modal-body">
            <form id="vedmg-schedule-lab-form">
                <input type="hidden" id="lab-student-id" name="student_id" value="">
                <input type="hidden" id="lab-enrollment-id" name="enrollment_id" value="">
                <input type="hidden" id="lab-course-id" name="course_id" value="">
                
                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="lab-session-title">Session Title:</label>
                        <input type="text" id="lab-session-title" name="session_title" class="vedmg-form-control" 
                               placeholder="Enter session title" required>
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="lab-session-type">Session Type:</label>
                        <select id="lab-session-type" name="session_type" class="vedmg-form-control" required>
                            <option value="">Select Type</option>
                            <option value="individual">Individual Session</option>
                            <option value="group">Group Session</option>
                            <option value="class">Class-wide Session</option>
                        </select>
                    </div>
                </div>
                
                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="lab-session-date">Session Date:</label>
                        <input type="date" id="lab-session-date" name="session_date" class="vedmg-form-control" required>
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="lab-session-time">Session Time:</label>
                        <input type="time" id="lab-session-time" name="session_time" class="vedmg-form-control" required>
                    </div>
                </div>
                
                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="lab-duration">Duration (minutes):</label>
                        <select id="lab-duration" name="duration" class="vedmg-form-control" required>
                            <option value="">Select Duration</option>
                            <option value="30">30 minutes</option>
                            <option value="45">45 minutes</option>
                            <option value="60">1 hour</option>
                            <option value="90">1.5 hours</option>
                            <option value="120">2 hours</option>
                            <option value="180">3 hours</option>
                        </select>
                    </div>
                </div>
                
                <div class="vedmg-form-group">
                    <label for="lab-description">Session Description:</label>
                    <textarea id="lab-description" name="description" class="vedmg-form-control" rows="3" 
                              placeholder="Enter session description and objectives"></textarea>
                </div>
                
                <div class="vedmg-form-group">
                    <label>
                        <input type="checkbox" id="lab-recurring" name="is_recurring" value="1">
                        Make this a recurring session
                    </label>
                </div>
                
                <div id="lab-recurring-options" style="display: none;">
                    <div class="vedmg-form-row">
                        <div class="vedmg-form-group vedmg-form-half">
                            <label for="lab-recurring-pattern">Repeat Pattern:</label>
                            <select id="lab-recurring-pattern" name="recurring_pattern" class="vedmg-form-control">
                                <option value="weekly">Weekly</option>
                                <option value="bi-weekly">Bi-weekly</option>
                                <option value="monthly">Monthly</option>
                                <option value="custom">Custom</option>
                            </select>
                        </div>
                        <div class="vedmg-form-group vedmg-form-half">
                            <label for="lab-recurring-count">Number of Sessions: <span class="vedmg-required">*</span></label>
                            <input type="number" id="lab-recurring-count" name="recurring_count" 
                                   class="vedmg-form-control" min="1" max="100" value="4" required>
                            <small class="vedmg-form-note">Maximum sessions that will be created (prevents infinite recurring)</small>
                        </div>
                    </div>
                    
                    <div class="vedmg-form-row">
                        <div class="vedmg-form-group">
                            <label for="lab-recurring-end-date">End Date: <span class="vedmg-required">*</span></label>
                            <input type="date" id="lab-recurring-end-date" name="recurring_end_date" class="vedmg-form-control" required>
                            <small class="vedmg-form-note">Sessions will stop at this date or when number of sessions is reached (whichever comes first)</small>
                        </div>
                    </div>
                    
                    <div class="vedmg-form-group" id="weekly-days-section">
                        <label>Days of the Week (for weekly patterns):</label>
                        <div class="vedmg-checkbox-group">
                            <label><input type="checkbox" name="recurring_days[]" value="monday"> Monday</label>
                            <label><input type="checkbox" name="recurring_days[]" value="tuesday"> Tuesday</label>
                            <label><input type="checkbox" name="recurring_days[]" value="wednesday"> Wednesday</label>
                            <label><input type="checkbox" name="recurring_days[]" value="thursday"> Thursday</label>
                            <label><input type="checkbox" name="recurring_days[]" value="friday"> Friday</label>
                            <label><input type="checkbox" name="recurring_days[]" value="saturday"> Saturday</label>
                            <label><input type="checkbox" name="recurring_days[]" value="sunday"> Sunday</label>
                        </div>
                    </div>
                    
                    <div class="vedmg-form-group" id="monthly-dates-section" style="display: none;">
                        <label>Dates of the Month (for monthly patterns):</label>
                        <div class="vedmg-checkbox-group vedmg-monthly-dates">
                            <?php for($i = 1; $i <= 31; $i++): ?>
                                <label><input type="checkbox" name="recurring_dates[]" value="<?php echo $i; ?>"> <?php echo $i; ?></label>
                            <?php endfor; ?>
                        </div>
                        <small class="vedmg-form-note">Select the dates of each month when sessions should occur. Invalid dates (like 31st for February) will be automatically skipped.</small>
                    </div>
                </div>
                
                <div class="vedmg-form-actions">
                    <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="cancel-schedule-lab">Cancel</button>
                    <button type="submit" class="vedmg-classroom-btn vedmg-classroom-btn-primary" id="save-schedule-lab">
                        Schedule Session
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Session Details Modal -->
<div id="vedmg-session-details-modal" class="vedmg-modal" style="display: none;">
    <div class="vedmg-modal-content">
        <div class="vedmg-modal-header">
            <h3>Session Details</h3>
            <span class="vedmg-modal-close" id="close-session-details">&times;</span>
        </div>
        <div class="vedmg-modal-body">
            <div id="session-details-content">
                <!-- Session details will be loaded here via AJAX -->
                <div class="vedmg-loading">Loading session details...</div>
            </div>
        </div>
        <div class="vedmg-modal-footer">
            <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="close-session-details-btn">Close</button>
        </div>
    </div>
</div>

<style>
/* Modal Styles - Same as courses.php for consistency */
.vedmg-modal {
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    overflow-y: auto;
}

.vedmg-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    animation: modalFadeIn 0.3s ease;
    position: relative;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.vedmg-modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 10;
    border-radius: 8px 8px 0 0;
}

.vedmg-modal-header h3 {
    margin: 0;
    color: #333;
}

.vedmg-modal-close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
    padding: 5px;
    line-height: 1;
    border: none;
    background: none;
    outline: none;
}

.vedmg-modal-close:hover {
    color: #000;
}

.vedmg-modal-body {
    padding: 25px;
}

.vedmg-modal-footer {
    padding: 15px 25px;
    border-top: 1px solid #ddd;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
    text-align: right;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.vedmg-form-group {
    margin-bottom: 20px;
}

.vedmg-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.vedmg-form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.vedmg-form-control:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,115,170,0.1);
}

.vedmg-form-row {
    display: flex;
    gap: 15px;
}

.vedmg-form-half {
    flex: 1;
}

.vedmg-form-actions {
    margin-top: 30px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.vedmg-form-group input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

/* Student Details Specific Styles */
.vedmg-student-details {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.vedmg-student-profile {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e5e5;
}

.vedmg-profile-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.vedmg-avatar-placeholder {
    color: white;
    font-size: 24px;
    font-weight: bold;
}

.vedmg-profile-info h4 {
    margin: 0 0 5px 0;
    font-size: 20px;
    font-weight: 600;
    color: #23282d;
}

.vedmg-profile-info p {
    margin: 0 0 8px 0;
    color: #666;
    font-size: 14px;
}

.vedmg-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.vedmg-detail-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.vedmg-detail-item label {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: 5px;
}

.vedmg-detail-item span {
    font-size: 16px;
    font-weight: 500;
    color: #23282d;
}

.vedmg-student-enrollments-list h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #23282d;
}

.vedmg-enrollment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 10px;
}

.vedmg-enrollment-item .course-name {
    font-weight: 600;
    color: #23282d;
    margin-bottom: 5px;
}

.vedmg-enrollment-item .enrollment-date {
    font-size: 12px;
    color: #666;
}

/* Responsive Design for Modals */
@media (max-width: 768px) {
    .vedmg-modal-content {
        width: 95%;
        margin: 2% auto;
    }
    
    .vedmg-modal-header,
    .vedmg-modal-body,
    .vedmg-modal-footer {
        padding: 15px;
    }
    
    .vedmg-student-profile {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .vedmg-details-grid {
        grid-template-columns: 1fr;
    }
    
    .vedmg-modal-footer {
        flex-direction: column;
    }
    
    .vedmg-form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .vedmg-form-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .vedmg-modal-content {
        width: 98%;
        margin: 1% auto;
    }
    
    .vedmg-modal-header h3 {
        font-size: 16px;
    }
}

/* Schedule Lab Modal Specific Styles */
.vedmg-classroom-btn-accent {
    background-color: #8E44AD;
    color: white;
    border: 1px solid #8E44AD;
}

/* Session Details Modal Specific Styles */
.vedmg-session-summary {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    text-align: center;
}

.vedmg-session-detail-card {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    margin-bottom: 15px;
    overflow: hidden;
}

.vedmg-session-header {
    background: #007cba;
    color: white;
    padding: 12px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.vedmg-session-header h4 {
    margin: 0;
    font-size: 16px;
}

.vedmg-session-type {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.vedmg-session-info {
    padding: 15px;
}

.vedmg-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.vedmg-info-item {
    padding: 8px;
    background: #f8f9fa;
    border-left: 3px solid #007cba;
    border-radius: 0 3px 3px 0;
}

.vedmg-info-item strong {
    color: #495057;
    display: block;
    margin-bottom: 4px;
}

.vedmg-session-description {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.vedmg-enrolled-students {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    padding: 12px;
    border-radius: 4px;
}

.vedmg-enrolled-students ul {
    margin: 10px 0 0 0;
    padding-left: 20px;
}

.vedmg-enrolled-students li {
    margin-bottom: 5px;
    color: #495057;
}

.vedmg-loading, .vedmg-error, .vedmg-info {
    text-align: center;
    padding: 40px 20px;
    font-size: 16px;
}

.vedmg-loading {
    color: #6c757d;
}

.vedmg-error {
    color: #dc3545;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
}

.vedmg-info {
    color: #0c5460;
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    border-radius: 4px;
}

/* Classroom Display Styles */
.vedmg-classroom-assigned {
    background-color: #e8f5e8;
    border: 1px solid #4caf50;
    border-radius: 4px;
    padding: 8px 10px;
    display: inline-block;
    min-width: 150px;
}

.vedmg-classroom-name {
    font-weight: 600;
    color: #2e7d32;
    display: block;
    margin-bottom: 2px;
}

.vedmg-classroom-id {
    color: #666;
    font-size: 11px;
    display: block;
}

.classroom-select {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
}

/* Group Selected Students Button */
#group-selected-students {
    margin-left: 10px;
    background-color: #0073aa;
    border-color: #005a87;
}

#group-selected-students:hover {
    background-color: #005a87;
    border-color: #004b72;
}

.vedmg-bulk-actions {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.vedmg-classroom-btn-accent:hover {
    background-color: #9B59B6;
    border-color: #9B59B6;
}

.vedmg-classroom-btn-primary {
    background-color: #4A90E2;
    color: white;
    border: 1px solid #4A90E2;
}

.vedmg-classroom-btn-primary:hover {
    background-color: #357ABD;
    border-color: #357ABD;
}

.vedmg-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 5px;
}

.vedmg-checkbox-group label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: normal;
    cursor: pointer;
    margin: 0;
}

.vedmg-checkbox-group input[type="checkbox"] {
    margin: 0;
}

/* Monthly dates specific styling */
.vedmg-monthly-dates {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    max-width: 400px;
}

.vedmg-monthly-dates label {
    justify-content: center;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f9f9f9;
    transition: all 0.2s ease;
}

.vedmg-monthly-dates label:hover {
    background: #e9e9e9;
    border-color: #ccc;
}

.vedmg-monthly-dates input[type="checkbox"]:checked + span,
.vedmg-monthly-dates label:has(input[type="checkbox"]:checked) {
    background: #007cba;
    color: white;
    border-color: #005a87;
}

.vedmg-form-note {
    display: block;
    margin-top: 8px;
    color: #666;
    font-style: italic;
    font-size: 12px;
}

/* Scheduled button state */
.vedmg-scheduled-btn {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: white !important;
}

.vedmg-scheduled-btn:hover {
    background-color: #218838 !important;
    border-color: #1e7e34 !important;
}

/* Inactive scheduled button state (no sessions) */
.vedmg-scheduled-btn-inactive {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

.vedmg-scheduled-btn-inactive:hover {
    background-color: #c82333 !important;
    border-color: #bd2130 !important;
}

/* Required field styling */
.vedmg-required {
    color: #dc3545;
    font-weight: bold;
}

.vedmg-form-control:required {
    border-left: 3px solid #007cba;
}

.vedmg-form-control:required:invalid {
    border-left-color: #dc3545;
}

.vedmg-form-control:required:valid {
    border-left-color: #28a745;
}

#lab-recurring-options {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-top: 10px;
    background-color: #f9f9f9;
}

#lab-recurring-options .vedmg-form-row {
    margin-bottom: 15px;
}

#lab-recurring-options .vedmg-form-row:last-child {
    margin-bottom: 0;
}

.vedmg-section-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .vedmg-checkbox-group {
        flex-direction: column;
        gap: 5px;
    }
    
    .vedmg-section-actions {
        flex-direction: column;
    }
    
    .vedmg-section-actions button {
        width: 100%;
    }
}
</style>

<script>
// Enrollment management specific JavaScript for server-side pagination
jQuery(document).ready(function($) {
    console.log('Student enrollments page loaded with server-side pagination');
    
    // Simple refresh functionality
    $('#refresh-enrollments').on('click', function(e) {
        e.preventDefault();
        $(this).prop('disabled', true).text('Refreshing...');
        setTimeout(function() {
            window.location.reload();
        }, 500);
    });
    
    // Handle checkbox selection for group button visibility
    function updateGroupButtonVisibility() {
        var checkedCount = $('.enrollment-checkbox:checked').length;
        if (checkedCount > 1) {
            $('#group-selected-students').show();
        } else {
            $('#group-selected-students').hide();
        }
    }
    
    // Bind checkbox events
    $('#select-all-enrollments').on('change', function() {
        $('.enrollment-checkbox').prop('checked', $(this).is(':checked'));
        updateGroupButtonVisibility();
    });
    
    $('.enrollment-checkbox').on('change', function() {
        var totalCheckboxes = $('.enrollment-checkbox').length;
        var checkedCheckboxes = $('.enrollment-checkbox:checked').length;
        $('#select-all-enrollments').prop('checked', checkedCheckboxes === totalCheckboxes);
        updateGroupButtonVisibility();
    });
    
    // Handle group selected students button click
    $('#group-selected-students').on('click', function() {
        var selectedStudents = $('.enrollment-checkbox:checked');
        if (selectedStudents.length > 1) {
            // Open schedule lab modal with group session pre-selected
            openGroupScheduleModal(selectedStudents);
        }
    });
    
    function openGroupScheduleModal(selectedStudents) {
        // Set session type to group and disable it
        $('#lab-session-type').val('group').prop('disabled', true);
        
        // Show modal
        $('#vedmg-schedule-lab-modal').fadeIn();
        
        console.log('Group schedule modal opened for', selectedStudents.length, 'students');
    }
    
    // Handle individual schedule lab button (normal behavior)
    $('.vedmg-schedule-lab-btn').on('click', function() {
        // Reset session type to allow individual/class selection only (hide group)
        var $sessionType = $('#lab-session-type');
        $sessionType.prop('disabled', false);
        $sessionType.empty();
        $sessionType.append('<option value="">Select Type</option>');
        $sessionType.append('<option value="individual">Individual Session</option>');
        $sessionType.append('<option value="class">Class-wide Session</option>');
        // Note: Group session is intentionally excluded for regular schedule lab button
        
        // Show modal
        $('#vedmg-schedule-lab-modal').fadeIn();
        
        console.log('Individual schedule modal opened');
    });
    
    // Handle recurring session checkbox change
    $('#lab-recurring').on('change', function() {
        var isChecked = $(this).is(':checked');
        var $recurringOptions = $('#lab-recurring-options');
        var $recurringPattern = $('#lab-recurring-pattern');
        var $recurringCount = $('#lab-recurring-count');
        var $recurringEndDate = $('#lab-recurring-end-date');
        
        if (isChecked) {
            $recurringOptions.slideDown();
            // Remove bi-weekly option when recurring is checked
            $recurringPattern.find('option[value="bi-weekly"]').remove();
            // Make fields required
            $recurringCount.prop('required', true);
            $recurringEndDate.prop('required', true);
            // Set minimum end date to tomorrow
            var tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            $recurringEndDate.attr('min', tomorrow.toISOString().split('T')[0]);
            // Trigger change to update days display
            $recurringPattern.trigger('change');
        } else {
            $recurringOptions.slideUp();
            // Add bi-weekly option back when unchecked
            if ($recurringPattern.find('option[value="bi-weekly"]').length === 0) {
                $recurringPattern.find('option[value="weekly"]').after('<option value="bi-weekly">Bi-weekly</option>');
            }
            // Remove required attribute
            $recurringCount.prop('required', false);
            $recurringEndDate.prop('required', false);
        }
    });
    
    // Handle recurring pattern dropdown change
    $('#lab-recurring-pattern').on('change', function() {
        var selectedPattern = $(this).val();
        var $weeklyDaysSection = $('#weekly-days-section');
        var $monthlyDatesSection = $('#monthly-dates-section');
        
        // Hide all sections first
        $weeklyDaysSection.hide();
        $monthlyDatesSection.hide();
        
        // Show relevant section based on pattern selection
        if (selectedPattern === 'weekly' || selectedPattern === 'bi-weekly') {
            $weeklyDaysSection.show();
            // Uncheck monthly dates when switching to weekly
            $('input[name="recurring_dates[]"]').prop('checked', false);
        } else if (selectedPattern === 'monthly') {
            $monthlyDatesSection.show();
            // Uncheck weekly days when switching to monthly
            $('input[name="recurring_days[]"]').prop('checked', false);
        } else {
            // For custom pattern, hide both and uncheck all
            $('input[name="recurring_days[]"]').prop('checked', false);
            $('input[name="recurring_dates[]"]').prop('checked', false);
        }
    });
    
    // Initialize recurring options state
    if ($('#lab-recurring').is(':checked')) {
        $('#lab-recurring-options').show();
        $('#lab-recurring-pattern').trigger('change');
    } else {
        $('#lab-recurring-options').hide();
    }
    
    // Handle form submission for scheduling sessions
    $('#vedmg-schedule-lab-form').on('submit', function(e) {
        e.preventDefault();
        
        var $form = $(this);
        var $submitBtn = $('#save-schedule-lab');
        
        // Enhanced validation for recurring sessions
        var isRecurring = $('#lab-recurring').is(':checked');
        if (isRecurring) {
            var recurringCount = parseInt($('#lab-recurring-count').val());
            var recurringEndDate = $('#lab-recurring-end-date').val();
            
            // Validate number of sessions
            if (!recurringCount || recurringCount < 1) {
                alert('Please enter a valid number of sessions (minimum 1).');
                $('#lab-recurring-count').focus();
                return;
            }
            
            if (recurringCount > 100) {
                alert('Number of sessions cannot exceed 100 for safety reasons.');
                $('#lab-recurring-count').focus();
                return;
            }
            
            // Validate end date
            if (!recurringEndDate) {
                alert('Please select an end date for recurring sessions.');
                $('#lab-recurring-end-date').focus();
                return;
            }
            
            // Validate end date is in the future
            var today = new Date();
            var endDate = new Date(recurringEndDate);
            if (endDate <= today) {
                alert('End date must be in the future.');
                $('#lab-recurring-end-date').focus();
                return;
            }
            
            // Validate pattern-specific requirements
            var pattern = $('#lab-recurring-pattern').val();
            if (pattern === 'weekly' || pattern === 'bi-weekly') {
                if ($('input[name="recurring_days[]"]:checked').length === 0) {
                    alert('Please select at least one day of the week for ' + pattern + ' sessions.');
                    return;
                }
            } else if (pattern === 'monthly') {
                if ($('input[name="recurring_dates[]"]:checked').length === 0) {
                    alert('Please select at least one date of the month for monthly sessions.');
                    return;
                }
            }
        }
        
        // Disable submit button and show loading
        $submitBtn.prop('disabled', true).text('Scheduling...');
        
        // Collect form data
        var formData = new FormData($form[0]);
        formData.append('action', 'vedmg_schedule_session');
        formData.append('nonce', '<?php echo wp_create_nonce("vedmg_schedule_session"); ?>');
        
        // Collect selected student IDs if it's a group session
        if ($('#lab-session-type').val() === 'group') {
            var selectedStudents = [];
            $('.enrollment-checkbox:checked').each(function() {
                selectedStudents.push($(this).data('student-id'));
            });
            formData.append('selected_student_ids', JSON.stringify(selectedStudents));
        }
        
        // Send AJAX request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    // Show success message
                    alert('Session scheduled successfully!');
                    
                    // Close modal
                    $('#vedmg-schedule-lab-modal').fadeOut();
                    
                    // Reset form
                    $form[0].reset();
                    $('#lab-recurring-options').hide();
                    
                    // Update button states based on response
                    if (response.data.session_id) {
                        updateScheduleButtonState(response.data.session_id, response.data.affected_students);
                    }
                    
                    // Reload page to show updated state
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                    
                } else {
                    alert('Error scheduling session: ' + (response.data || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                alert('Error scheduling session: ' + error);
            },
            complete: function() {
                // Re-enable submit button
                $submitBtn.prop('disabled', false).text('Schedule Session');
            }
        });
    });
    
    // Function to update schedule button states
    function updateScheduleButtonState(sessionId, affectedStudents) {
        affectedStudents.forEach(function(studentId) {
            // Update the Schedule Lab button
            var $scheduleBtn = $('.vedmg-schedule-lab-btn[data-student-id="' + studentId + '"]');
            $scheduleBtn.removeClass('vedmg-classroom-btn-accent')
                       .addClass('vedmg-classroom-btn-success vedmg-scheduled-btn')
                       .text('Scheduled')
                       .data('session-id', sessionId);
            
            // Update the Scheduled button to active state
            var $scheduledBtn = $('.vedmg-scheduled-btn-inactive[data-student-id="' + studentId + '"], .vedmg-scheduled-btn[data-student-id="' + studentId + '"]');
            $scheduledBtn.removeClass('vedmg-classroom-btn-danger vedmg-scheduled-btn-inactive')
                        .addClass('vedmg-classroom-btn-success vedmg-scheduled-btn')
                        .text('Scheduled')
                        .attr('data-has-sessions', '1')
                        .data('student-id', studentId);
        });
    }
    
    // Handle click on "Scheduled" button to show session details
    $(document).on('click', '.vedmg-scheduled-btn, .vedmg-scheduled-btn-inactive', function(e) {
        e.preventDefault();
        var studentId = $(this).data('student-id');
        var hasSessions = $(this).data('has-sessions');
        
        if (hasSessions === '1' || hasSessions === 1) {
            // Student has sessions, show details modal
            showSessionDetailsModal(studentId);
        } else {
            // Student has no sessions, show message
            alert('No sessions scheduled for this student yet. Use "Schedule Lab" button to create sessions.');
        }
    });
    
    // Function to show session details modal
    function showSessionDetailsModal(studentId) {
        var $modal = $('#vedmg-session-details-modal');
        var $content = $('#session-details-content');
        
        // Show loading state
        $content.html('<div class="vedmg-loading">Loading session details...</div>');
        $modal.fadeIn();
        
        // Fetch session details via AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'vedmg_get_session_details',
                student_id: studentId,
                nonce: '<?php echo wp_create_nonce("vedmg_get_session_details"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    displaySessionDetails(response.data.sessions);
                } else {
                    $content.html('<div class="vedmg-error">Error: ' + response.data + '</div>');
                }
            },
            error: function() {
                $content.html('<div class="vedmg-error">Failed to load session details. Please try again.</div>');
            }
        });
    }
    
    // Function to display session details in the modal
    function displaySessionDetails(sessions) {
        var $content = $('#session-details-content');
        var html = '';
        
        if (!sessions || sessions.length === 0) {
            html = '<div class="vedmg-info">No sessions found for this student.</div>';
        } else {
            html += '<div class="vedmg-session-summary">';
            html += '<h4>Total Sessions: ' + sessions.length + '</h4>';
            html += '</div>';
            
            sessions.forEach(function(session, index) {
                html += '<div class="vedmg-session-detail-card">';
                html += '<div class="vedmg-session-header">';
                html += '<h4>' + session.session_title + '</h4>';
                html += '<span class="vedmg-session-type">' + session.session_type + '</span>';
                html += '</div>';
                
                html += '<div class="vedmg-session-info">';
                html += '<div class="vedmg-info-grid">';
                
                // Basic session info
                html += '<div class="vedmg-info-item">';
                html += '<strong>Date:</strong> ' + session.session_date;
                html += '</div>';
                
                html += '<div class="vedmg-info-item">';
                html += '<strong>Time:</strong> ' + session.session_time;
                html += '</div>';
                
                html += '<div class="vedmg-info-item">';
                html += '<strong>Duration:</strong> ' + session.duration;
                html += '</div>';
                
                html += '<div class="vedmg-info-item">';
                html += '<strong>Instructor:</strong> ' + session.instructor;
                html += '</div>';
                
                html += '<div class="vedmg-info-item">';
                html += '<strong>Course:</strong> ' + session.course_name;
                html += '</div>';
                
                html += '<div class="vedmg-info-item">';
                html += '<strong>Total Enrolled:</strong> ' + session.total_enrolled;
                html += '</div>';
                
                // Recurring info
                if (session.is_recurring === 'Yes') {
                    html += '<div class="vedmg-info-item">';
                    html += '<strong>Recurring:</strong> ' + session.recurring_pattern;
                    html += '</div>';
                    
                    html += '<div class="vedmg-info-item">';
                    html += '<strong>Sessions Count:</strong> ' + session.recurring_count;
                    html += '</div>';
                    
                    html += '<div class="vedmg-info-item">';
                    html += '<strong>End Date:</strong> ' + session.recurring_end_date;
                    html += '</div>';
                }
                
                html += '</div>'; // End info-grid
                
                // Description
                if (session.description && session.description !== 'No description provided') {
                    html += '<div class="vedmg-session-description">';
                    html += '<strong>Description:</strong><br>' + session.description;
                    html += '</div>';
                }
                
                // Enrolled students
                if (session.enrolled_students && session.enrolled_students.length > 0) {
                    html += '<div class="vedmg-enrolled-students">';
                    html += '<strong>Enrolled Students:</strong>';
                    html += '<ul>';
                    session.enrolled_students.forEach(function(student) {
                        html += '<li>' + student.student_name + ' (' + student.student_email + ')</li>';
                    });
                    html += '</ul>';
                    html += '</div>';
                }
                
                html += '</div>'; // End session-info
                html += '</div>'; // End session-detail-card
            });
        }
        
        $content.html(html);
    }
    
    // Handle modal close events
    $('#close-session-details, #close-session-details-btn').on('click', function() {
        $('#vedmg-session-details-modal').fadeOut();
    });
    
    // Close modal when clicking outside
    $('#vedmg-session-details-modal').on('click', function(e) {
        if (e.target === this) {
            $(this).fadeOut();
        }
    });
    
    // Any additional enrollment-specific JavaScript will be handled by enrollments.js
});
</script>
