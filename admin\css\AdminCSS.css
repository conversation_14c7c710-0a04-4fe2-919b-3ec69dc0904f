/**
 * VedMG ClassRoom Admin CSS
 * 
 * Styles for the admin interface of the VedMG ClassRoom plugin.
 * This file contains all CSS styles for admin pages.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

/* Main admin container */
.vedmg-classroom-admin {
    margin: 20px 0;
}

/* Admin page header */
.vedmg-classroom-header {
    background: #fff;
    padding: 20px;
    border: 1px solid #e5e5e5;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
    margin-bottom: 20px;
}

.vedmg-classroom-header h1 {
    margin: 0 0 10px 0;
    font-size: 24px;
    color: #23282d;
}

.vedmg-classroom-header p {
    margin: 0;
    color: #666;
}

/* Section Header and Action Styling */
.vedmg-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.vedmg-section-header h2 {
    margin: 0;
    color: #333;
    font-size: 24px;
    font-weight: 600;
}

.vedmg-section-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.vedmg-section-actions .vedmg-classroom-btn {
    min-width: auto;
    white-space: nowrap;
}

/* Admin content sections */
.vedmg-classroom-section {
    background: white;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.vedmg-classroom-section h2 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 20px;
    font-weight: 600;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

/* Tables */
.vedmg-classroom-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.vedmg-classroom-table th,
.vedmg-classroom-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e5e5e5;
}

.vedmg-classroom-table th {
    background: #f9f9f9;
    font-weight: 600;
}

.vedmg-classroom-table tr:hover {
    background: #f9f9f9;
}

/* Buttons */
.vedmg-classroom-btn {
    display: inline-block;
    padding: 8px 16px;
    background: #0073aa;
    color: #fff;
    text-decoration: none;
    border-radius: 3px;
    border: none;
    cursor: pointer;
    font-size: 13px;
}

.vedmg-classroom-btn:hover {
    background: #005a87;
    color: #fff;
}

.vedmg-classroom-btn-secondary {
    background: #666;
}

.vedmg-classroom-btn-secondary:hover {
    background: #555;
}

.vedmg-classroom-btn-danger {
    background: #dc3232;
}

.vedmg-classroom-btn-danger:hover {
    background: #ba2626;
}

.vedmg-classroom-btn-primary {
    background: #0073aa;
    border-color: #0073aa;
}

.vedmg-classroom-btn-primary:hover {
    background: #005a87;
    border-color: #005a87;
}

.vedmg-classroom-btn-warning {
    background: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.vedmg-classroom-btn-warning:hover {
    background: #e0a800;
    border-color: #d39e00;
    color: #212529;
}

.vedmg-classroom-btn-success {
    background: #28a745;
    border-color: #28a745;
}

.vedmg-classroom-btn-success:hover {
    background: #218838;
    border-color: #1e7e34;
}

/* Filter Controls Styling */
.vedmg-filter-controls {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.vedmg-filter-group {
    display: flex;
    flex-direction: column;
    min-width: 200px;
}

.vedmg-filter-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.vedmg-filter-select,
.vedmg-filter-input {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    background: white;
}

.vedmg-filter-select:focus,
.vedmg-filter-input:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,115,170,0.1);
}

/* Debug section */
.vedmg-classroom-debug {
    background: #f0f0f0;
    border-left: 4px solid #0073aa;
    padding: 15px;
    margin-top: 20px;
}

.vedmg-classroom-debug-log {
    background: #000;
    color: #0f0;
    padding: 15px;
    font-family: monospace;
    font-size: 12px;
    max-height: 300px;
    overflow-y: auto;
    border-radius: 3px;
}

/* Status indicators */
.vedmg-status-active {
    color: #46b450;
    font-weight: 600;
    background: #f0fff0;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

.vedmg-status-inactive {
    color: #dc3232;
    font-weight: 600;
    background: #fff0f0;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

.vedmg-status-pending {
    color: #ffb900;
    font-weight: 600;
    background: #fffbf0;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

.vedmg-status-success {
    color: #00a32a;
    font-weight: 600;
    background: #e8f5e8;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

/* Loading spinners - DISABLED */
.vedmg-classroom-spinner {
    display: none !important;
    width: 14px;
    height: 14px;
    border: 2px solid #fff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 5px;
    vertical-align: middle;
}

.vedmg-classroom-btn .vedmg-classroom-spinner {
    display: none !important;
    margin-right: 5px;
}

.vedmg-classroom-btn.loading .vedmg-classroom-spinner {
    display: none !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Real vs Placeholder Data Styling */
.real-data-row {
    background-color: #f9fff9;
    border-left: 3px solid #46b450;
}

.placeholder-data-row {
    background-color: #fafafa;
    border-left: 3px solid #ddd;
}

.real-data-row:hover {
    background-color: #f0fff0;
}

.placeholder-data-row:hover {
    background-color: #f0f0f0;
}

/* Data type indicators */
.real-data-row small {
    color: #46b450 !important;
    font-weight: 600;
}

.placeholder-data-row small {
    color: #999 !important;
    font-style: italic;
}

/* Database status info box */
.database-status-info {
    background: #f0f0f1;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-top: 20px;
}

.database-status-info h4 {
    margin: 0 0 10px 0;
    color: #23282d;
}

.database-status-info p {
    margin: 0;
    color: #666;
    line-height: 1.5;
}

/* Responsive improvements */
@media (max-width: 1200px) {
    .vedmg-classroom-table {
        font-size: 13px;
    }
    
    .vedmg-classroom-table th,
    .vedmg-classroom-table td {
        padding: 8px 6px;
    }
}

@media (max-width: 768px) {
    .real-data-row,
    .placeholder-data-row {
        border-left-width: 2px;
    }
    
    .vedmg-classroom-table small {
        display: none; /* Hide data type indicators on mobile */
    }
    
    .vedmg-section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .vedmg-section-actions {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
    }
    
    .vedmg-filter-controls {
        padding: 15px;
    }
    
    .vedmg-classroom-section {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .vedmg-section-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .vedmg-section-actions .vedmg-classroom-btn {
        width: 100%;
        justify-content: center;
    }
    
    .vedmg-classroom-section {
        padding: 15px;
    }
}
