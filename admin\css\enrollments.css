/**
 * VedMG ClassRoom Enrollments CSS
 * 
 * Specific styles for the student enrollments page.
 * Contains styles for enrollment tables, filters, and enrollment-specific elements.
 * Supports server-side pagination.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

/* Filter Controls */
.vedmg-filter-controls {
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
    margin-top: 15px;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 6px;
    border: 1px solid #e5e5e5;
}

.vedmg-filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 150px;
}

.vedmg-filter-group label {
    font-size: 13px;
    font-weight: 600;
    color: #23282d;
}

.vedmg-filter-select,
.vedmg-filter-input {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
    background: #fff;
}

.vedmg-filter-select:focus,
.vedmg-filter-input:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

/* Enrollment Summary */
.vedmg-enrollment-summary {
    display: flex;
    gap: 20px;
    font-size: 14px;
    color: #666;
}

.vedmg-enrollment-summary strong {
    color: #23282d;
}

/* Server-side Pagination Styles */
.vedmg-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 25px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.vedmg-pagination-info {
    color: #666;
    font-size: 14px;
}

.vedmg-pagination-controls {
    display: flex;
    align-items: center;
    gap: 5px;
}

.vedmg-pagination-btn {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-size: 14px;
    display: inline-block;
}

.vedmg-pagination-btn:hover:not(.disabled) {
    background: #e9ecef;
    border-color: #adb5bd;
    text-decoration: none;
    color: #495057;
}

.vedmg-pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f8f9fa;
}

.vedmg-pagination-btn.active {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.vedmg-pagination-numbers {
    display: flex;
    gap: 2px;
}

/* Missing User Account Warning */
.enrollment-row td small {
    font-style: italic;
    color: #d63638;
    font-size: 11px;
}

/* Bulk Actions */
.vedmg-bulk-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    margin: 15px 0;
    padding: 10px;
    background: #f0f0f1;
    border-radius: 4px;
}

.vedmg-bulk-actions select {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
}

/* Enrollment Status */
.vedmg-enrollment-status[data-status="pending"] {
    background: #fff3cd;
    color: #856404;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
}

.vedmg-enrollment-status[data-status="enrolled"],
.vedmg-enrollment-status[data-status="active"] {
    background: #d4edda;
    color: #155724;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
}

.vedmg-enrollment-status[data-status="inactive"] {
    background: #f8d7da;
    color: #721c24;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
}

/* Classroom Select */
.classroom-select {
    width: 100%;
    max-width: 180px;
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 12px;
    background: #fff;
}

.classroom-select:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

/* Enrollment Statistics */
.vedmg-enrollment-stats {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
    margin-top: 15px;
}

.vedmg-enrollment-stats .vedmg-stat-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.vedmg-enrollment-stats .vedmg-stat-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.vedmg-enrollment-stats .vedmg-stat-value {
    font-size: 20px;
    font-weight: bold;
    color: #0073aa;
}

/* Checkbox Styling */
.enrollment-checkbox {
    margin: 0;
    transform: scale(1.1);
}

#select-all-enrollments {
    margin: 0;
    transform: scale(1.1);
}

/* View Student Button */
.vedmg-view-student-btn {
    font-size: 11px;
    padding: 4px 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .vedmg-filter-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }
    
    .vedmg-filter-group {
        min-width: auto;
    }
    
    .vedmg-enrollment-summary {
        flex-direction: column;
        gap: 8px;
    }
    
    .vedmg-bulk-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .vedmg-enrollment-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .vedmg-enrollment-stats .vedmg-stat-item {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        background: #f9f9f9;
        border-radius: 4px;
    }
    
    .classroom-select {
        max-width: 100%;
        font-size: 11px;
    }
    
    .vedmg-pagination {
        flex-direction: column;
        gap: 15px;
    }
    
    .vedmg-pagination-controls {
        order: -1;
    }
}

@media (max-width: 480px) {
    .vedmg-filter-controls {
        padding: 10px;
    }
    
    .vedmg-action-buttons {
        flex-direction: column;
        gap: 5px;
    }
    
    .vedmg-action-buttons .vedmg-classroom-btn {
        text-align: center;
        font-size: 11px;
        padding: 6px 8px;
    }
    
    .vedmg-enrollment-summary {
        font-size: 13px;
    }
}

/* Modal Styles */
.vedmg-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
}

.vedmg-modal-content {
    background-color: #fff;
    margin: 50px auto;
    padding: 0;
    border: none;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: vedmg-modal-slideIn 0.3s ease-out;
}

@keyframes vedmg-modal-slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.vedmg-modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e5e5e5;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.vedmg-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #23282d;
}

.vedmg-modal-close {
    font-size: 24px;
    font-weight: bold;
    color: #666;
    cursor: pointer;
    background: none;
    border: none;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.vedmg-modal-close:hover {
    color: #000;
    background-color: #f0f0f0;
}

.vedmg-modal-body {
    padding: 25px;
    max-height: 500px;
    overflow-y: auto;
}

.vedmg-modal-footer {
    padding: 15px 25px;
    border-top: 1px solid #e5e5e5;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
    text-align: right;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Student Details Modal Specific Styles */
.vedmg-student-details {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.vedmg-student-profile {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e5e5;
}

.vedmg-profile-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.vedmg-avatar-placeholder {
    color: white;
    font-size: 24px;
    font-weight: bold;
}

.vedmg-profile-info h4 {
    margin: 0 0 5px 0;
    font-size: 20px;
    font-weight: 600;
    color: #23282d;
}

.vedmg-profile-info p {
    margin: 0 0 8px 0;
    color: #666;
    font-size: 14px;
}

.vedmg-enrollment-status {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    text-transform: capitalize;
}

.vedmg-enrollment-status[data-status="enrolled"] {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.vedmg-enrollment-status[data-status="pending"] {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.vedmg-enrollment-status[data-status="completed"] {
    background-color: #cce7ff;
    color: #004085;
    border: 1px solid #b3d7ff;
}

.vedmg-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.vedmg-detail-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.vedmg-detail-item label {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: 5px;
}

.vedmg-detail-item span {
    font-size: 16px;
    font-weight: 500;
    color: #23282d;
}

.vedmg-student-enrollments-list h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #23282d;
}

.vedmg-enrollment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 10px;
}

.vedmg-enrollment-item .course-name {
    font-weight: 600;
    color: #23282d;
    margin-bottom: 5px;
}

.vedmg-enrollment-item .enrollment-date {
    font-size: 12px;
    color: #666;
}

/* Modal Responsive Design */
@media (max-width: 768px) {
    .vedmg-modal-content {
        width: 95%;
        margin: 20px auto;
    }
    
    .vedmg-modal-header,
    .vedmg-modal-body,
    .vedmg-modal-footer {
        padding: 15px;
    }
    
    .vedmg-student-profile {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .vedmg-details-grid {
        grid-template-columns: 1fr;
    }
    
    .vedmg-modal-footer {
        flex-direction: column;
    }
}
