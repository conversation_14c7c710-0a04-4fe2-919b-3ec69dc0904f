<?php
/**
 * VedMG ClassRoom Admin Dashboard (Redesigned)
 * 
 * This is the main dashboard page for the VedMG ClassRoom plugin.
 * It displays quick statistics and links to other admin pages.
 * Simplified to show only overview and navigation.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR> @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

// Include database helper
require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'database/helper.php';

// Log page access
vedmg_log_admin_action('Viewed dashboard page');

// Get dashboard statistics
$dashboard_stats = VedMG_ClassRoom_Database_Helper::get_dashboard_stats();
?>

<div class="vedmg-classroom-admin">
    <!-- Page Header -->
    <div class="vedmg-classroom-header">
        <h1>VedMG ClassRoom Dashboard</h1>
        <p>Welcome to your classroom management center. Get an overview and navigate to different sections.</p>
    </div>
    
    <!-- Quick Statistics Section -->
    <div class="vedmg-classroom-section">
        <h2>Quick Statistics</h2>
        <div class="vedmg-stats-grid">
            <div class="vedmg-stat-card">
                <div class="vedmg-stat-number"><?php echo $dashboard_stats['courses']; ?></div>
                <div class="vedmg-stat-label">Total Courses</div>
                <div class="vedmg-stat-note">
                    <?php if ($dashboard_stats['courses'] > 0): ?>
                        <small class="real-data">Real Data</small>
                    <?php else: ?>
                        <small class="no-data">No data yet</small>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="vedmg-stat-card">
                <div class="vedmg-stat-number"><?php echo $dashboard_stats['enrollments']; ?></div>
                <div class="vedmg-stat-label">Total Students</div>
                <div class="vedmg-stat-note">
                    <?php if ($dashboard_stats['enrollments'] > 0): ?>
                        <small class="real-data">Real Data</small>
                    <?php else: ?>
                        <small class="no-data">No data yet</small>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="vedmg-stat-card">
                <div class="vedmg-stat-number"><?php echo $dashboard_stats['active_classrooms']; ?></div>
                <div class="vedmg-stat-label">Active Classrooms</div>
                <div class="vedmg-stat-note">
                    <?php if ($dashboard_stats['active_classrooms'] > 0): ?>
                        <small class="real-data">Real Data</small>
                    <?php else: ?>
                        <small class="no-data">No data yet</small>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="vedmg-stat-card">
                <div class="vedmg-stat-number"><?php echo $dashboard_stats['pending_enrollments']; ?></div>
                <div class="vedmg-stat-label">Pending Enrollments</div>
                <div class="vedmg-stat-note">
                    <?php if ($dashboard_stats['pending_enrollments'] > 0): ?>
                        <small class="real-data">Real Data</small>
                    <?php else: ?>
                        <small class="no-data">No data yet</small>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Navigation Section -->
    <div class="vedmg-classroom-section">
        <h2>Quick Navigation</h2>
        <p>Access different sections of your classroom management system:</p>
        
        <div class="vedmg-nav-grid">
            <div class="vedmg-nav-card">
                <div class="vedmg-nav-icon">📚</div>
                <h3>Course Management</h3>
                <p>Manage courses, instructors, and Google Classroom integration</p>
                <a href="<?php echo admin_url('admin.php?page=vedmg-classroom-courses'); ?>" class="vedmg-classroom-btn">
                    Manage Courses
                </a>
            </div>
            
            <div class="vedmg-nav-card">
                <div class="vedmg-nav-icon">👥</div>
                <h3>Student Enrollments</h3>
                <p>View and manage student enrollments and classroom assignments</p>
                <a href="<?php echo admin_url('admin.php?page=vedmg-classroom-enrollments'); ?>" class="vedmg-classroom-btn">
                    Manage Enrollments
                </a>
            </div>
            
            <div class="vedmg-nav-card">
                <div class="vedmg-nav-icon">🕐</div>
                <h3>Class Sessions</h3>
                <p>Schedule and manage Google Meet sessions for your courses</p>
                <a href="<?php echo admin_url('admin.php?page=vedmg-classroom-sessions'); ?>" class="vedmg-classroom-btn">
                    Manage Sessions
                </a>
            </div>
            
            <div class="vedmg-nav-card">
                <div class="vedmg-nav-icon">👨‍🏫</div>
                <h3>Instructor Roster</h3>
                <p>View and manage instructor information and assignments</p>
                <a href="<?php echo admin_url('admin.php?page=vedmg-classroom-instructors'); ?>" class="vedmg-classroom-btn">
                    View Instructors
                </a>
            </div>
        </div>
    </div>
    
    <!-- System Status Section -->
    <div class="vedmg-classroom-section">
        <h2>System Status</h2>
        <div class="vedmg-system-status">
            <div class="vedmg-status-item">
                <span class="vedmg-status-label">Database Status:</span>
                <span class="vedmg-status-value">
                    <?php if ($dashboard_stats['courses'] > 0 || $dashboard_stats['enrollments'] > 0): ?>
                        <span class="vedmg-status-active">✅ Active & Connected</span>
                    <?php else: ?>
                        <span class="vedmg-status-pending">⚠️ Ready (No data yet)</span>
                    <?php endif; ?>
                </span>
            </div>
            
            <div class="vedmg-status-item">
                <span class="vedmg-status-label">Plugin Version:</span>
                <span class="vedmg-status-value"><?php echo VEDMG_CLASSROOM_VERSION; ?></span>
            </div>
            
            <div class="vedmg-status-item">
                <span class="vedmg-status-label">Debug Logging:</span>
                <span class="vedmg-status-value">
                    <?php if (get_option('vedmg_classroom_debug_enabled', false)): ?>
                        <span class="vedmg-status-active">✅ Enabled</span>
                    <?php else: ?>
                        <span class="vedmg-status-inactive">❌ Disabled</span>
                    <?php endif; ?>
                </span>
            </div>
        </div>
        
        <div style="margin-top: 15px;">
            <a href="<?php echo admin_url('admin.php?page=vedmg-classroom-settings'); ?>" class="vedmg-classroom-btn vedmg-classroom-btn-secondary">
                Configure Settings
            </a>
        </div>
    </div>
    
    <!-- Test Button for Development -->
    <div class="vedmg-classroom-section">
        <h2>Development Tools</h2>
        <p>Tools for testing and debugging the plugin functionality:</p>
        
        <button class="vedmg-classroom-btn vedmg-test-btn">
            <span class="vedmg-classroom-spinner"></span>
            Test AJAX Connection
        </button>
    </div>
</div>

<script>
// Dashboard-specific JavaScript
jQuery(document).ready(function($) {
    console.log('Dashboard page loaded');
    
    // Add any dashboard-specific functionality here
    $('.vedmg-nav-card').hover(
        function() {
            $(this).addClass('hover');
        },
        function() {
            $(this).removeClass('hover');
        }
    );
});
</script>
