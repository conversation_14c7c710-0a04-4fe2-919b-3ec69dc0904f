## Reference Plugins

### Our Plugin Ecosystem
Our VedMG ClassRoom plugin integrates with the following existing plugins to create a complete e-learning management system:

- **MasterStudy LMS:** A comprehensive LMS plugin for WordPress, providing course management, quizzes, and student profiles.
  - **PATH:** "C:\xampp\htdocs\paylearn\wp-content\plugins\masterstudy-lms-learning-management-system"
  - **Purpose:** Manages course content, instructors, and basic student enrollment
  - **Data We Use:** Course information, instructor details, student enrollment data

- **WooCommerce:** The leading eCommerce plugin for WordPress, enabling online sales and payment processing.
  - **PATH:** "C:\xampp\htdocs\paylearn\wp-content\plugins\woocommerce"
  - **Purpose:** Handles course sales and payment processing
  - **Data We Use:** Purchase records, customer information (name, email, phone)

- **Vedmg woo lms:** A custom plugin that integrates with WooCommerce to manage courses and student enrollments, providing a clean interface for course management.
  - **PATH:** "C:\xampp\htdocs\paylearn\wp-content\plugins\Vedmg-woo-LMS"
  - **Purpose:** Bridges WooCommerce purchases with MasterStudy LMS enrollments
  - **Data We Use:** Enrollment mappings, purchase-to-course relationships

### How Our Plugin Fits In
1. **MasterStudy LMS** creates courses and manages instructors
2. **WooCommerce** handles course sales and customer data
3. **Vedmg-woo-LMS** connects purchases to course enrollments
4. **Our VedMG ClassRoom** adds Google Classroom integration and advanced admin management

### Data Flow Overview
```
Course Creation (MasterStudy) → Course Sale (WooCommerce) → 
Enrollment Link (Vedmg-woo-LMS) → Google Classroom Assignment (Our Plugin)
```

### Key Database Tables We'll Reference
- **MasterStudy LMS Tables:** Course data, instructor information
- **WooCommerce Tables:** Customer details, order information
- **Vedmg-woo-LMS Tables:** Enrollment mappings and relationships

### Our Plugin's Role
- **Extends the ecosystem** with Google Classroom functionality
- **Provides admin interface** for post-purchase course management
- **Manages Google Classroom assignments** (placeholder now, API later)
- **Tracks and logs all operations** for transparency and debugging

These plugins serve as the foundation for our VedMG ClassRoom plugin, which adds the missing Google Classroom integration and advanced admin management capabilities.
