<?php
/**
 * Enhanced VedMG ClassRoom Demo Data Cleanup Script
 * 
 * This script removes all demo data created by the Workflow_Test_Script.php
 * including the enhanced Google Classroom integration data and instructor sync data.
 * 
 * Usage: Run this script after completing demonstrations to clean up the database.
 * 
 * @package VedMG_ClassRoom
 * @version 2.0 Enhanced
 */

// Include WordPress
require_once(__DIR__ . '/../../../wp-config.php');

if (!function_exists('add_action')) {
    die('❌ Error: WordPress environment not loaded properly.');
}

global $wpdb;

$start_time = microtime(true);

echo "🗑️ VedMG ClassRoom Enhanced Demo Data Cleanup\n";
echo str_repeat("=", 55) . "\n";
echo "🚀 Starting comprehensive cleanup process...\n\n";

try {
    // Step 1: Delete instructor sync data
    echo "👨‍🏫 STEP 1: Cleaning Instructor Sync Data\n";
    echo str_repeat("-", 40) . "\n";
    
    $instructor_sync_deleted = $wpdb->query("DELETE FROM {$wpdb->prefix}vedmg_instructor_sync WHERE 1=1");
    echo "   ✅ Deleted {$instructor_sync_deleted} instructor sync records\n\n";
    
    // Step 2: Delete Google Classroom student mappings
    echo "🗺️ STEP 2: Cleaning Google Classroom Mappings\n";
    echo str_repeat("-", 45) . "\n";
    
    $mappings_deleted = $wpdb->query("DELETE FROM {$wpdb->prefix}vedmg_student_classroom_mappings WHERE 1=1");
    echo "   ✅ Deleted {$mappings_deleted} Google Classroom student mappings\n\n";
    
    // Step 3: Delete class sessions
    echo "📅 STEP 3: Cleaning Class Sessions\n";
    echo str_repeat("-", 35) . "\n";
    
    $sessions_deleted = $wpdb->query("DELETE FROM {$wpdb->prefix}vedmg_class_sessions WHERE 1=1");
    echo "   ✅ Deleted {$sessions_deleted} class session records\n\n";
    
    // Step 4: Delete student enrollments
    echo "👨‍🎓 STEP 4: Cleaning Student Enrollments\n";
    echo str_repeat("-", 40) . "\n";
    
    $enrollments_deleted = $wpdb->query("DELETE FROM {$wpdb->prefix}vedmg_student_enrollments WHERE 1=1");
    echo "   ✅ Deleted {$enrollments_deleted} student enrollment records\n\n";
    
    // Step 5: Delete courses
    echo "📚 STEP 5: Cleaning Course Data\n";
    echo str_repeat("-", 30) . "\n";
    
    $courses_deleted = $wpdb->query("DELETE FROM {$wpdb->prefix}vedmg_courses WHERE 1=1");
    echo "   ✅ Deleted {$courses_deleted} course records\n\n";
    
    // Step 6: Delete demo user accounts
    echo "👤 STEP 6: Cleaning Demo User Accounts\n";
    echo str_repeat("-", 35) . "\n";
    
    // Find all demo users by email pattern
    $demo_emails = [
        '%@vedmg.in%',
        '%@instructor-demo.com%', 
        '%@student-demo.com%'
    ];
    
    $users_deleted = 0;
    foreach ($demo_emails as $email_pattern) {
        $demo_users = $wpdb->get_results($wpdb->prepare(
            "SELECT ID FROM {$wpdb->prefix}users WHERE user_email LIKE %s",
            $email_pattern
        ));
        
        foreach ($demo_users as $user) {
            // Delete user meta
            $wpdb->delete("{$wpdb->prefix}usermeta", ['user_id' => $user->ID]);
            
            // Delete user
            $wpdb->delete("{$wpdb->prefix}users", ['ID' => $user->ID]);
            $users_deleted++;
        }
    }
    
    echo "   ✅ Deleted {$users_deleted} demo user accounts\n\n";
    
    // Step 7: Reset auto-increment IDs (optional)
    echo "🔄 STEP 7: Resetting Database Sequences\n";
    echo str_repeat("-", 38) . "\n";
    
    $tables_to_reset = [
        $wpdb->prefix . 'vedmg_courses',
        $wpdb->prefix . 'vedmg_student_enrollments',
        $wpdb->prefix . 'vedmg_class_sessions',
        $wpdb->prefix . 'vedmg_student_classroom_mappings',
        $wpdb->prefix . 'vedmg_instructor_sync'
    ];
    
    foreach ($tables_to_reset as $table) {
        $wpdb->query("ALTER TABLE {$table} AUTO_INCREMENT = 1");
    }
    
    echo "   ✅ Reset auto-increment sequences for all tables\n\n";
    
    // Step 8: Verify cleanup completion
    echo "✅ STEP 8: Verification & Final Report\n";
    echo str_repeat("-", 38) . "\n";
    
    $verification_stats = [
        'courses' => $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}vedmg_courses"),
        'enrollments' => $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}vedmg_student_enrollments"),
        'sessions' => $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}vedmg_class_sessions"),
        'mappings' => $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}vedmg_student_classroom_mappings"),
        'instructor_sync' => $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}vedmg_instructor_sync")
    ];
    
    echo "   📊 Post-Cleanup Verification:\n";
    echo "      - Courses remaining: {$verification_stats['courses']}\n";
    echo "      - Enrollments remaining: {$verification_stats['enrollments']}\n";
    echo "      - Sessions remaining: {$verification_stats['sessions']}\n";
    echo "      - Classroom mappings remaining: {$verification_stats['mappings']}\n";
    echo "      - Instructor sync records remaining: {$verification_stats['instructor_sync']}\n\n";
    
    // Calculate execution time
    $end_time = microtime(true);
    $execution_time = round(($end_time - $start_time) * 1000, 2);
    
    echo "📋 CLEANUP COMPLETION SUMMARY\n";
    echo str_repeat("=", 35) . "\n";
    echo "✅ Cleanup Success Rate: 100%\n";
    echo "⏱️ Total Execution Time: {$execution_time}ms\n";
    echo "🗑️ Records Deleted:\n";
    echo "   - Instructor sync: {$instructor_sync_deleted}\n";
    echo "   - Google Classroom mappings: {$mappings_deleted}\n";
    echo "   - Class sessions: {$sessions_deleted}\n";
    echo "   - Student enrollments: {$enrollments_deleted}\n";
    echo "   - Courses: {$courses_deleted}\n";
    echo "   - Demo users: {$users_deleted}\n\n";
    
    $total_records = $instructor_sync_deleted + $mappings_deleted + $sessions_deleted + 
                    $enrollments_deleted + $courses_deleted + $users_deleted;
    
    echo "🎯 TOTAL RECORDS CLEANED: {$total_records}\n\n";
    
    echo "====================================================================\n";
    echo "✅ ENHANCED DEMO DATA CLEANUP COMPLETED SUCCESSFULLY!\n";
    echo "🧹 VedMG ClassRoom database is now clean and ready for fresh demos\n";
    echo "🚀 You can run Workflow_Test_Script.php again to create new demo data\n";
    echo "====================================================================\n";
    
} catch (Exception $e) {
    echo "❌ CRITICAL ERROR during cleanup: " . $e->getMessage() . "\n";
    echo "Cleanup process failed! Some data may remain in the database.\n";
    echo "Manual database cleanup may be required.\n";
}

?>
