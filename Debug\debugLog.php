<?php
/**
 * VedMG ClassRoom Debug Logger
 * 
 * This file manages all debug logging for the VedMG ClassRoom plugin.
 * It provides functions to log different types of events and errors.
 * 
 * Features:
 * - Enable/disable debugging from admin panel
 * - Different log levels (INFO, ERROR, WARNING, ACTION)
 * - Automatic log rotation to prevent large files
 * - Formatted log entries with timestamps
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR> @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

/**
 * VedMG ClassRoom Debug Logger Class
 * 
 * Handles all logging operations for the plugin.
 * Provides methods to log different types of events and manage log files.
 */
class VedMG_ClassRoom_Debug_Logger {
    
    /**
     * @var string The path to the debug log file
     */
    private static $log_file;
    
    /**
     * @var bool Whether debugging is enabled (default: false)
     */
    private static $debug_enabled = false;
    
    /**
     * @var int Maximum log file size in bytes (5MB)
     */
    private static $max_log_size = 5242880; // 5MB
    
    /**
     * Initialize the logger
     * Sets up the log file path and checks if debugging is enabled
     */
    public static function init() {
        // Set the log file path (in the plugin root directory)
        // Use dirname to go up one level from Debug folder to plugin root
        if (function_exists('plugin_dir_path')) {
            self::$log_file = dirname(plugin_dir_path(__FILE__)) . '/debug.log';
        } else {
            // Fallback if WordPress functions aren't available
            self::$log_file = dirname(__DIR__) . '/debug.log';
        }
        
        // Check if debugging is enabled from WordPress options
        // Default to false if option doesn't exist or WordPress isn't loaded
        if (function_exists('get_option')) {
            self::$debug_enabled = get_option('vedmg_classroom_debug_enabled', false);
        } else {
            self::$debug_enabled = false;
        }
        
        // Create log file if it doesn't exist
        if (!file_exists(self::$log_file)) {
            self::create_log_file();
        }
    }
    
    /**
     * Enable or disable debugging
     * 
     * @param bool $enabled Whether to enable debugging
     */
    public static function set_debug_enabled($enabled) {
        self::$debug_enabled = $enabled;
        
        // Update WordPress option if available
        if (function_exists('update_option')) {
            update_option('vedmg_classroom_debug_enabled', $enabled);
        }
        
        // Log the change
        if ($enabled) {
            self::log_info('DEBUG', 'Debug logging enabled');
        } else {
            self::log_info('DEBUG', 'Debug logging disabled');
        }
    }
    
    /**
     * Check if debugging is enabled
     * 
     * @return bool Whether debugging is enabled
     */
    public static function is_debug_enabled() {
        // Always get the current value from WordPress options
        if (function_exists('get_option')) {
            self::$debug_enabled = get_option('vedmg_classroom_debug_enabled', 0);
        }
        return (bool) self::$debug_enabled;
    }
    
    /**
     * Log an informational message
     * 
     * @param string $component The component that generated the log (e.g., 'CORE', 'DATABASE', 'ADMIN')
     * @param string $message The message to log
     */
    public static function log_info($component, $message) {
        self::write_log('INFO', $component, $message);
    }
    
    /**
     * Log an error message
     * 
     * @param string $component The component that generated the error
     * @param string $message The error message
     * @param string $details Additional error details (optional)
     */
    public static function log_error($component, $message, $details = '') {
        $full_message = $message;
        if (!empty($details)) {
            $full_message .= ' | Details: ' . $details;
        }
        self::write_log('ERROR', $component, $full_message);
    }
    
    /**
     * Log a warning message
     * 
     * @param string $component The component that generated the warning
     * @param string $message The warning message
     */
    public static function log_warning($component, $message) {
        self::write_log('WARNING', $component, $message);
    }
    
    /**
     * Log an admin action
     * 
     * @param string $action The action performed
     * @param string $details Additional details about the action
     */
    public static function log_admin_action($action, $details = '') {
        $user_info = 'Unknown User';
        
        // Get current user info if WordPress is available
        if (function_exists('wp_get_current_user')) {
            $user = wp_get_current_user();
            if ($user && $user->ID) {
                $user_info = $user->user_login . ' (ID: ' . $user->ID . ')';
            }
        }
        
        $message = "Admin action: $action | User: $user_info";
        if (!empty($details)) {
            $message .= ' | Details: ' . $details;
        }
        
        self::write_log('ACTION', 'ADMIN', $message);
    }
    
    /**
     * Log a database operation
     * 
     * @param string $operation The database operation (SELECT, INSERT, UPDATE, DELETE)
     * @param string $table The table name
     * @param string $details Additional details about the operation
     */
    public static function log_database_operation($operation, $table, $details = '') {
        $message = "Database $operation on table: $table";
        if (!empty($details)) {
            $message .= ' | Details: ' . $details;
        }
        
        self::write_log('DATABASE', 'DB', $message);
    }
    
    /**
     * Write a log entry to the log file
     * 
     * @param string $level The log level (INFO, ERROR, WARNING, ACTION, DATABASE)
     * @param string $component The component that generated the log
     * @param string $message The message to log
     */
    private static function write_log($level, $component, $message) {
        // Check if debugging is enabled by checking WordPress option directly
        $debug_enabled = false;
        if (function_exists('get_option')) {
            $debug_enabled = get_option('vedmg_classroom_debug_enabled', 0);
        }
        
        // Only write to log if debugging is enabled
        if (!$debug_enabled) {
            return;
        }
        
        // Initialize if not already done
        if (empty(self::$log_file)) {
            self::init();
        }
        
        // Check log file size and rotate if necessary
        self::rotate_log_if_needed();
        
        // Format the log entry
        $timestamp = function_exists('current_time') ? current_time('Y-m-d H:i:s') : date('Y-m-d H:i:s');
        $log_entry = "[$timestamp] [$level] [$component] $message" . PHP_EOL;
        
        // Write to log file with proper UTF-8 encoding
        // Use file_put_contents with LOCK_EX and FILE_APPEND for better encoding control
        file_put_contents(self::$log_file, $log_entry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Create the initial log file with header information
     */
    private static function create_log_file() {
        $init_time = function_exists('current_time') ? current_time('Y-m-d H:i:s') : date('Y-m-d H:i:s');
        $wp_version = function_exists('get_bloginfo') ? get_bloginfo('version') : 'Unknown';
        
        $header = "=== VedMG ClassRoom Plugin Debug Log - ENABLED ===" . PHP_EOL;
        $header .= "Plugin initialized on: " . $init_time . PHP_EOL;
        $header .= "WordPress Version: " . $wp_version . PHP_EOL;
        $header .= "Plugin Version: 1.0" . PHP_EOL;
        $header .= "PHP Version: " . phpversion() . PHP_EOL;
        $header .= PHP_EOL;
        $header .= "Log Format: [TIMESTAMP] [LEVEL] [COMPONENT] MESSAGE" . PHP_EOL;
        $header .= PHP_EOL;
        $header .= "=== Log Entries ===" . PHP_EOL;
        
        // Write with UTF-8 encoding and no BOM
        file_put_contents(self::$log_file, $header, LOCK_EX);
    }
    
    /**
     * Rotate log file if it gets too large
     * Renames current log to .old and creates a new one
     */
    private static function rotate_log_if_needed() {
        if (file_exists(self::$log_file) && filesize(self::$log_file) > self::$max_log_size) {
            // Create backup of current log
            $backup_file = self::$log_file . '.old';
            if (file_exists($backup_file)) {
                unlink($backup_file); // Remove old backup
            }
            
            // Move current log to backup
            rename(self::$log_file, $backup_file);
            
            // Create new log file
            self::create_log_file();
            
            // Log the rotation
            self::write_log('INFO', 'LOGGER', 'Log file rotated due to size limit');
        }
    }
    
    /**
     * Get the current log file contents
     * 
     * @param int $lines Number of lines to return (default: 100)
     * @return string The log file contents
     */
    public static function get_log_contents($lines = 100) {
        if (!file_exists(self::$log_file)) {
            return "No log file found.";
        }
        
        $log_contents = file_get_contents(self::$log_file);
        
        // If requesting limited lines, get only the last N lines
        if ($lines > 0) {
            $log_array = explode(PHP_EOL, $log_contents);
            $log_array = array_slice($log_array, -$lines);
            $log_contents = implode(PHP_EOL, $log_array);
        }
        
        return $log_contents;
    }
    
    /**
     * Clear the log file
     */
    public static function clear_log() {
        if (file_exists(self::$log_file)) {
            unlink(self::$log_file);
        }
        self::create_log_file();
        self::log_info('LOGGER', 'Log file cleared');
    }
    
    /**
     * Get log file size in human readable format
     * 
     * @return string The log file size
     */
    public static function get_log_size() {
        if (!file_exists(self::$log_file)) {
            return "0 bytes";
        }
        
        $size = filesize(self::$log_file);
        
        if ($size < 1024) {
            return $size . ' bytes';
        } elseif ($size < 1048576) {
            return round($size / 1024, 2) . ' KB';
        } else {
            return round($size / 1048576, 2) . ' MB';
        }
    }
}

// Initialize the logger when this file is included
VedMG_ClassRoom_Debug_Logger::init();

/**
 * Convenience functions for logging
 * These functions provide easy access to the logger methods
 */

/**
 * Log an informational message
 * 
 * @param string $component The component generating the log
 * @param string $message The message to log
 */
function vedmg_log_info($component, $message) {
    VedMG_ClassRoom_Debug_Logger::log_info($component, $message);
}

/**
 * Log an error message
 * 
 * @param string $component The component generating the error
 * @param string $message The error message
 * @param string $details Additional error details
 */
function vedmg_log_error($component, $message, $details = '') {
    VedMG_ClassRoom_Debug_Logger::log_error($component, $message, $details);
}

/**
 * Log a warning message
 * 
 * @param string $component The component generating the warning
 * @param string $message The warning message
 */
function vedmg_log_warning($component, $message) {
    VedMG_ClassRoom_Debug_Logger::log_warning($component, $message);
}

/**
 * Log an admin action
 * 
 * @param string $action The action performed
 * @param string $details Additional details
 */
function vedmg_log_admin_action($action, $details = '') {
    VedMG_ClassRoom_Debug_Logger::log_admin_action($action, $details);
}

/**
 * Log a database operation
 * 
 * @param string $operation The database operation
 * @param string $table The table name
 * @param string $details Additional details
 */
function vedmg_log_database($operation, $table, $details = '') {
    VedMG_ClassRoom_Debug_Logger::log_database_operation($operation, $table, $details);
}

?>
