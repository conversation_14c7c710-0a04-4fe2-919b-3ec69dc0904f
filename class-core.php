<?php
/**
 * VedMG ClassRoom Core Class
 * 
 * This is the main class that handles all core functionality of the plugin.
 * It manages initialization, admin interface, and coordinates between different components.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

/**
 * Main VedMG ClassRoom Core Class
 * 
 * Handles plugin initialization and coordination between different components
 */
class VedMG_ClassRoom_Core {
    
    /**
     * @var string Plugin version
     */
    private static $version = '1.0';
    
    /**
     * @var bool Whether the plugin is initialized
     */
    private static $initialized = false;
    
    /**
     * Initialize the plugin
     * Sets up all necessary hooks and includes required files
     */
    public static function init() {
        // Prevent double initialization
        if (self::$initialized) {
            return;
        }
        
        // Log initialization start
        vedmg_log_info('CORE', 'Starting core initialization');
        
        // Load admin functionality if we're in admin area
        if (is_admin()) {
            self::load_admin();
        }
        
        // Mark as initialized
        self::$initialized = true;
        
        // Log successful initialization
        vedmg_log_info('CORE', 'Core initialization completed');
    }
    
    /**
     * Load admin functionality
     * Includes and initializes admin-related files
     */
    private static function load_admin() {
        // Include database helper
        require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'database/helper.php';
        
        // Include admin files
        require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'admin/admin.php';
        
        // Initialize admin
        VedMG_ClassRoom_Admin::init();
        
        // Log admin loading
        vedmg_log_info('CORE', 'Admin functionality loaded');
    }
    
    /**
     * Plugin activation
     * Runs when the plugin is activated
     */
    public static function activate() {
        // Log activation
        vedmg_log_info('CORE', 'Plugin activation started');
        
        // Create database tables if needed
        self::create_database_tables();
        
        // Set default options
        self::set_default_options();
        
        // Log successful activation
        vedmg_log_info('CORE', 'Plugin activation completed');
    }
    
    /**
     * Plugin deactivation
     * Runs when the plugin is deactivated
     */
    public static function deactivate() {
        // Log deactivation
        vedmg_log_info('CORE', 'Plugin deactivation started');
        
        // Include database deactivator
        require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'database/deactivator.php';
        
        // Ask user if they want to keep data or remove everything
        // For now, we'll preserve data by default (safer option)
        // To completely remove data, change the parameter to true
        $delete_all_data = false; // Set to true if you want to remove all data
        
        // Deactivate database (with data preservation option)
        VedMG_ClassRoom_Database_Deactivator::deactivate($delete_all_data);
        
        if ($delete_all_data) {
            // Verify complete removal if data was deleted
            if (VedMG_ClassRoom_Database_Deactivator::verify_complete_removal()) {
                vedmg_log_info('DATABASE', 'Complete data removal verified');
            } else {
                vedmg_log_warning('DATABASE', 'Some plugin data may still exist');
            }
        }
        
        // Log successful deactivation
        vedmg_log_info('CORE', 'Plugin deactivation completed');
    }
    
    /**
     * Create database tables
     * Creates the necessary tables for the plugin
     */
    private static function create_database_tables() {
        // Include database activator
        require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'database/activator.php';
        
        // Activate database tables
        VedMG_ClassRoom_Database_Activator::activate();
        
        // Verify tables were created successfully
        if (VedMG_ClassRoom_Database_Activator::verify_tables_exist()) {
            vedmg_log_info('DATABASE', 'All database tables created and verified successfully');
        } else {
            vedmg_log_error('DATABASE', 'Database table creation verification failed');
        }
    }
    
    /**
     * Set default options
     * Sets default WordPress options for the plugin
     */
    private static function set_default_options() {
        // Set default debug option to false
        add_option('vedmg_classroom_debug_enabled', false);
        
        // Set default console logging option to false
        add_option('vedmg_classroom_console_logging_enabled', false);
        
        // Set plugin version
        add_option('vedmg_classroom_version', self::$version);
        
        // Log default options set
        vedmg_log_info('CORE', 'Default options set');
    }
    
    /**
     * Get plugin version
     * 
     * @return string The plugin version
     */
    public static function get_version() {
        return self::$version;
    }
    
    /**
     * Check if plugin is initialized
     * 
     * @return bool Whether the plugin is initialized
     */
    public static function is_initialized() {
        return self::$initialized;
    }
    
    /**
     * Get database statistics
     * Returns information about plugin database usage
     * 
     * @return array Database statistics
     */
    public static function get_database_stats() {
        // Include database activator for stats
        require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'database/activator.php';
        
        return VedMG_ClassRoom_Database_Activator::get_database_stats();
    }
    
    /**
     * Verify database integrity
     * Checks if all required tables exist and are properly structured
     * 
     * @return bool True if database is intact, false otherwise
     */
    public static function verify_database_integrity() {
        // Include database activator for verification
        require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'database/activator.php';
        
        return VedMG_ClassRoom_Database_Activator::verify_tables_exist();
    }
    
    /**
     * Repair database if needed
     * Attempts to recreate missing tables
     * 
     * @return bool True if repair was successful, false otherwise
     */
    public static function repair_database() {
        vedmg_log_info('DATABASE', 'Starting database repair');
        
        try {
            // Check current state
            if (self::verify_database_integrity()) {
                vedmg_log_info('DATABASE', 'Database is intact, no repair needed');
                return true;
            }
            
            // Attempt to recreate missing tables
            self::create_database_tables();
            
            // Verify repair was successful
            $repair_successful = self::verify_database_integrity();
            
            if ($repair_successful) {
                vedmg_log_info('DATABASE', 'Database repair completed successfully');
            } else {
                vedmg_log_error('DATABASE', 'Database repair failed');
            }
            
            return $repair_successful;
            
        } catch (Exception $e) {
            vedmg_log_error('DATABASE', 'Database repair exception', $e->getMessage());
            return false;
        }
    }
}

?>
