# VedMG ClassRoom Plugin - Senior Testing Guide

## 🎯 Overview
This guide contains everything your senior needs to test the VedMG ClassRoom plugin comprehensively. The testing scripts will create demo data, test all functionality, and provide a complete demonstration of the plugin capabilities.

## 📋 Prerequisites
- WordPress installation with admin access
- XAMPP or similar local server environment
- PHP command line access
- VedMG ClassRoom plugin installed and activated

## 🚀 Quick Start Guide

### Step 1: Run the Demo Script
Navigate to the plugin directory and execute the master demo script:

```terminal
C:\xampp\php\php.exe "c:\xampp\htdocs\paylearn\wp-content\plugins\VedMG-ClassRoom\script-name.php"
```

### Step 2: Explore the Admin Panel
After running the demo script, log into WordPress admin and navigate to:
- **VedMG ClassRoom** menu in the admin sidebar
- Check all sub-pages: Dashboard, Courses, Enrollments, Sessions, Instructors

### Step 3: Clean Up (When Done)
After testing, clean all demo data:

```terminal
C:\xampp\php\php.exe "c:\xampp\htdocs\paylearn\wp-content\plugins\VedMG-ClassRoom\script-name.php"
```

---

## 📊 Expected Output from Demo Script

### ✅ Successful Demo Script Output Should Show:

```
====================================================================
🎓 VedMG ClassRoom Plugin - Senior Testing Demo Script
====================================================================

🔧 STEP 1: Initializing Demo Environment
==================================================
   ✅ Cleaned table: wp_vedmg_courses (0 records removed)
   ✅ Cleaned table: wp_vedmg_student_enrollments (0 records removed)
   ✅ Cleaned table: wp_vedmg_class_sessions (0 records removed)
   ✅ Mock Google APIs initialized
   ✅ Demo environment ready

👨‍🏫 STEP 2: Creating Demo Instructors
========================================
   ✅ Created instructor: Dr. Sarah Johnson
      - Email: <EMAIL>
      - Specialization: Web Development & Programming

   ✅ Created instructor: Prof. Michael Chen
      - Email: <EMAIL>
      - Specialization: Data Science & Analytics

   ✅ Created instructor: Dr. Emily Rodriguez
      - Email: <EMAIL>
      - Specialization: Digital Marketing & Design

📚 STEP 3: Creating Demo Courses
=================================
   ✅ Created course: Complete WordPress Development Masterclass
      - Instructor: Dr. Sarah Johnson
      - Duration: 12 weeks
      - Google Classroom: gc_[unique_id]

   ✅ Created course: Data Science with Python & Machine Learning
      - Instructor: Prof. Michael Chen
      - Duration: 16 weeks
      - Google Classroom: gc_[unique_id]

   ✅ Created course: Digital Marketing & Brand Strategy
      - Instructor: Dr. Emily Rodriguez
      - Duration: 8 weeks
      - Google Classroom: gc_[unique_id]

👨‍🎓 STEP 4: Creating Demo Students & Enrollments
================================================
   ✅ Enrolled: John Smith in Complete WordPress Development Masterclass
      - Email: <EMAIL>
      - Phone: ******-0101
      - Google Classroom: Enrolled

   [Additional enrollments...]

📅 STEP 5: Creating Demo Sessions
=================================
   ✅ Created session: Complete WordPress Development Masterclass - Orientation & Introduction
      - Date: [tomorrow] at 10:00:00
      - Type: orientation
      - Google Meet: [unique_id]

   [Additional sessions...]

🎛️ STEP 6: Testing Admin Panel Functionality
=============================================
   📊 Dashboard Statistics:
      - Total Courses: 3
      - Total Students: 8
      - Total Sessions: 9
      - Active Sessions: 9
      - Total Instructors: 3

   📚 Courses Page Test:
      ✅ Complete WordPress Development Masterclass
         - Instructor: Dr. Sarah Johnson
         - Students: 4
         - Sessions: 3
         - Status: active

   [Additional course details...]

🔌 STEP 7: Testing API Integrations
===================================
   🏫 Google Classroom API Test:
      ✅ Classroom Creation: Working
      ✅ Student Enrollment: Working

   📹 Google Meet API Test:
      ✅ Meeting Creation: Working
      ✅ Meeting Link: https://meet.google.com/[unique_id]

   📅 Google Calendar API Test:
      ✅ Event Creation: Working
      ✅ Event Link: https://calendar.google.com/calendar/event?eid=[unique_id]

📋 STEP 8: Final Demo Report
============================
📊 DEMO COMPLETION SUMMARY:
----------------------------------------
   ✅ Success Rate: 100% (7/7)
   ⏱️ Total Execution Time: [X]ms

📋 DETAILED TEST RESULTS:
----------------------------------------
   ✅ Initialization: PASSED
   ✅ Instructors: PASSED
   ✅ Courses: PASSED
   ✅ Students: PASSED
   ✅ Sessions: PASSED
   ✅ Admin panel: PASSED
   ✅ Api integrations: PASSED

📊 DEMO DATA CREATED:
----------------------------------------
   👨‍🏫 Instructors: 3
   📚 Courses: 3
   👨‍🎓 Students Enrolled: 8
   📅 Sessions Created: 9

====================================================================
✅ DEMO SCRIPT COMPLETED SUCCESSFULLY!
🎓 VedMG ClassRoom Plugin is ready for senior testing.
====================================================================
```

---

## 🎛️ What to Test in Admin Panel

After running the demo script, you should see the following in WordPress admin:

### Dashboard Page
- **Statistics Overview**: Total courses, students, sessions, instructors
- **Recent Activity**: Latest enrollments and sessions
- **Quick Actions**: Links to create new courses, sessions

### Courses Page
- **Course List**: 3 demo courses with complete details
- **Course Details**: Instructor info, enrollment count, session count
- **Google Classroom Integration**: Active classroom IDs and links

### Enrollments Page
- **Student List**: 8 enrolled students across courses
- **Enrollment Details**: Contact info, enrollment status, payment status
- **Course Assignment**: Which students are in which courses

### Sessions Page
- **Session Schedule**: 9 sessions across all courses
- **Session Types**: Orientation, lab, presentation sessions
- **Google Meet Integration**: Meeting links and room IDs
- **Calendar Integration**: Event links and scheduling

### Instructors Page
- **Instructor List**: 3 demo instructors with specializations
- **Course Assignment**: Which instructor teaches which courses
- **Contact Information**: Email addresses and profiles

---

## 🎯 Instructor Modal Testing

### Testing Instructor Details Modal
The demo script creates comprehensive data to test the instructor modal's session display functionality.

**To test the instructor modal:**
1. **Navigate to**: WordPress Admin → VedMG ClassRoom → Instructors
2. **Click**: "View Details" button for any instructor  
3. **Verify the modal shows**:
   - Instructor basic information (name, email, specialization)
   - **Today's Classes section** with current day sessions
   - **Upcoming Classes section** with next 7 days
   - Course assignments and student counts

### Expected Modal Content
**Today's Sessions** (2025-08-09):
- Each instructor has 1 "Introduction Session" scheduled for today
- Time: 10:00 AM - 12:00 PM
- Shows course name and enrolled student count

**Upcoming Sessions** (Next 7 Days):
- 3 additional sessions per instructor over the next week
- Orientation session (tomorrow)
- Weekly Lab Session (in 3 days)  
- Project Presentation (in 7 days)

### Demo Instructor Data
- **Dr. Sarah Johnson**: WordPress Development (4 students, 4 sessions)
- **Prof. Michael Chen**: Data Science (2 students, 4 sessions)  
- **Dr. Emily Rodriguez**: Digital Marketing (2 students, 4 sessions)

### Troubleshooting Modal Issues
If modal shows "Loading..." or empty data:
1. Check browser console for JavaScript errors
2. Verify AJAX endpoints are responding
3. Confirm instructor ID is passed correctly
4. Check that sessions have proper `instructor_id` assignment

## 🔍 Testing Checklist

### ✅ Core Functionality Testing
- [ ] **Plugin Activation**: Plugin activates without errors
- [ ] **Database Tables**: All required tables are created
- [ ] **Admin Menu**: VedMG ClassRoom menu appears in admin
- [ ] **Demo Data Creation**: All demo data created successfully
- [ ] **Data Display**: All admin pages show demo data correctly

### ✅ Integration Testing
- [ ] **Google Classroom**: Classroom IDs generated and displayed
- [ ] **Google Meet**: Meeting links created for all sessions
- [ ] **Google Calendar**: Calendar event links generated
- [ ] **WordPress Users**: Demo users created and assigned roles
- [ ] **Database Relationships**: All foreign keys working correctly

### ✅ User Interface Testing
- [ ] **Responsive Design**: Admin pages work on different screen sizes
- [ ] **Navigation**: All menu links work correctly
- [ ] **Data Tables**: Pagination and sorting functional
- [ ] **Forms**: All forms submit and validate properly
- [ ] **Instructor Modal**: View Details shows today's and upcoming sessions
- [ ] **Error Handling**: Graceful error messages when needed

### ✅ Performance Testing
- [ ] **Page Load Times**: Admin pages load quickly
- [ ] **Database Queries**: No slow queries or N+1 problems
- [ ] **Memory Usage**: Reasonable memory consumption
- [ ] **Script Execution**: Demo script completes in reasonable time

---

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. **Script Execution Fails**
```
Fatal Error: Failed opening required '../../../wp-config.php'
```
**Solution**: Run the script from the correct directory (plugin root folder)

#### 2. **Database Connection Error**
```
Error: Could not connect to database
```
**Solution**: Ensure WordPress is properly configured and database is running

#### 3. **Permission Errors**
```
Fatal Error: Call to undefined function wp_create_user()
```
**Solution**: Ensure WordPress is loaded correctly and all required files are included

#### 4. **No Demo Data Visible**
```
Admin panel shows no courses/students/sessions
```
**Solution**: Check if demo script completed successfully, verify database tables exist

#### 5. **Plugin Not Activated**
```
VedMG ClassRoom menu not visible in admin
```
**Solution**: Activate the plugin through WordPress admin > Plugins page

### Debug Mode
To see detailed output and debug information, edit the script and add:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

---

