<?php
/**
 * VedMG ClassRoom Settings Page
 * 
 * This page allows administrators to configure plugin settings.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

// Get current settings directly from WordPress options
$debug_enabled = get_option('vedmg_classroom_debug_enabled', 0);
$console_logging_enabled = get_option('vedmg_classroom_console_logging_enabled', 0);

// Check if settings were just updated
$settings_updated = isset($_GET['settings-updated']) && $_GET['settings-updated'] === 'true';

// Get log information
$log_size = '0 bytes';
$log_contents = 'No log entries found.';

if ($debug_enabled) {
    // Only load logger if debug is enabled
    if (class_exists('VedMG_ClassRoom_Debug_Logger')) {
        $log_size = VedMG_ClassRoom_Debug_Logger::get_log_size();
        $log_contents = VedMG_ClassRoom_Debug_Logger::get_log_contents(20);
    }
}

// Log page access
vedmg_log_admin_action('Viewed settings page');
?>

<div class="vedmg-classroom-admin">
    <!-- Page Header -->
    <div class="vedmg-classroom-header">
        <h1>VedMG ClassRoom Settings</h1>
        <p>Configure plugin settings and manage debug options</p>
    </div>
    
    <!-- Show success message if settings were updated -->
    <?php if ($settings_updated): ?>
        <div class="notice notice-success is-dismissible">
            <p>Settings saved successfully!</p>
        </div>
    <?php endif; ?>
    
    <!-- Settings Form -->
    <div class="vedmg-classroom-section">
        <!-- <h2>General Settings</h2> -->
        
        <form method="post" action="options.php" class="vedmg-classroom-form">
            <?php
            // Output security fields for the registered setting
            settings_fields('vedmg_classroom_settings');
            
            // Output setting sections and their fields
            do_settings_sections('vedmg_classroom_settings');
            
            // Output save settings button
            submit_button('Save Settings', 'primary', 'submit', true, array('class' => 'vedmg-classroom-btn'));
            ?>
        </form>
    </div>
    
    <!-- Debug Information -->
    <?php if ($debug_enabled): ?>
    <div class="vedmg-classroom-section vedmg-classroom-debug">
        <h2>Debug Information</h2>
        
        <div class="vedmg-classroom-form-group">
            <label>Log File Size: <?php echo $log_size; ?></label>
        </div>
        
        <div class="vedmg-classroom-form-group">
            <label>Recent Log Entries:</label>
            <div class="vedmg-classroom-debug-log">
                <?php echo nl2br(htmlspecialchars($log_contents)); ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- System Information -->
    <div class="vedmg-classroom-section">
        <h2>System Information</h2>
        
        <table class="vedmg-classroom-table">
            <tr>
                <td><strong>Plugin Version:</strong></td>
                <td><?php echo VedMG_ClassRoom_Core::get_version(); ?></td>
            </tr>
            <tr>
                <td><strong>WordPress Version:</strong></td>
                <td><?php echo get_bloginfo('version'); ?></td>
            </tr>
            <tr>
                <td><strong>PHP Version:</strong></td>
                <td><?php echo phpversion(); ?></td>
            </tr>
            <tr>
                <td><strong>Debug Mode:</strong></td>
                <td><?php echo $debug_enabled ? '<span class="vedmg-status-active">Enabled</span>' : '<span class="vedmg-status-inactive">Disabled</span>'; ?></td>
            </tr>
            <tr>
                <td><strong>Console Logging:</strong></td>
                <td><?php echo $console_logging_enabled ? '<span class="vedmg-status-active">Enabled</span>' : '<span class="vedmg-status-inactive">Disabled</span>'; ?></td>
            </tr>
            <tr>
                <td><strong>Plugin Directory:</strong></td>
                <td><?php echo VEDMG_CLASSROOM_PLUGIN_DIR; ?></td>
            </tr>
        </table>
    </div>
</div>

<script>
// Additional JavaScript for settings page
jQuery(document).ready(function($) {
    console.log('Settings page loaded');
    
    // Show/hide debug section based on checkbox
    $('#vedmg_classroom_debug_enabled').trigger('change');
    
    // Handle console logging toggle
    $('#vedmg_classroom_console_logging_enabled').on('change', function() {
        var isEnabled = $(this).is(':checked');
        if (isEnabled) {
            vedmg_console_log('Console logging will be enabled after saving settings');
        } else {
            vedmg_console_log('Console logging will be disabled after saving settings');
        }
    });
});
</script>
