<?php
/**
 * VedMG ClassRoom Instructor Roster Page
 * 
 * This page handles instructor management functionality.
 * Allows viewing instructors, their courses, and managing instructor information.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

// Include database helper
require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'database/helper.php';

// Log page access
vedmg_log_admin_action('Viewed instructor roster page');

// Handle pagination parameters
$current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$per_page = isset($_GET['per_page']) ? max(1, intval($_GET['per_page'])) : 10;
$search_term = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';

// Get instructor data from database with pagination
$instructors_data = VedMG_ClassRoom_Database_Helper::get_instructors_paginated($current_page, $per_page, '', $search_term);
$instructors = $instructors_data['instructors'];
$total_instructors = $instructors_data['total'];
$total_pages = ceil($total_instructors / $per_page);

// Calculate pagination info
$start_item = (($current_page - 1) * $per_page) + 1;
$end_item = min($current_page * $per_page, $total_instructors);

// Build current URL for pagination links
$base_url = admin_url('admin.php?page=vedmg-classroom-instructors');
$url_params = array();
if ($per_page != 10) $url_params['per_page'] = $per_page;
if ($search_term) $url_params['search'] = $search_term;
$base_url .= !empty($url_params) ? '&' . http_build_query($url_params) : '';
?>

<div class="vedmg-classroom-admin">
    <!-- Page Header -->
    <div class="vedmg-classroom-header">
        <h1>Instructor Roster</h1>
        <p>View and manage instructor information and assignments</p>
    </div>
    
    <!-- Instructor Management Controls -->
    <div class="vedmg-classroom-section">
        <!-- <div class="vedmg-section-header">
            <h2>Instructor Management</h2>
            <div class="vedmg-section-actions">
                
                <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="refresh-instructors">
                    Refresh Data
                </button>
            </div>
        </div> -->
        
        <!-- Instructor Search -->
        <form method="GET" class="vedmg-search-form">
            <input type="hidden" name="page" value="<?php echo esc_attr($_GET['page'] ?? ''); ?>">
            
            <div class="vedmg-search-controls">
                <div class="vedmg-search-group">
                    <label for="search">Search Instructors:</label>
                    <input type="text" name="search" id="search" 
                           value="<?php echo esc_attr($search_term); ?>" 
                           class="vedmg-search-input" 
                           placeholder="Search by name, email, or phone number...">
                </div>
                
                <div class="vedmg-search-group">
                    <label for="per_page">Items per page:</label>
                    <select name="per_page" id="per_page" class="vedmg-search-select">
                        <option value="10" <?php selected($per_page, 10); ?>>10</option>
                        <option value="25" <?php selected($per_page, 25); ?>>25</option>
                        <option value="50" <?php selected($per_page, 50); ?>>50</option>
                        <option value="100" <?php selected($per_page, 100); ?>>100</option>
                    </select>
                </div>
                
                <div class="vedmg-search-group">
                    <button type="submit" class="vedmg-classroom-btn">Search</button>
                    <a href="<?php echo admin_url('admin.php?page=vedmg-classroom-instructors'); ?>" 
                       class="vedmg-classroom-btn vedmg-classroom-btn-secondary">Clear</a>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Instructor Roster Table -->
    <div class="vedmg-classroom-section">
        <div class="vedmg-section-header">
            <h2>All Instructors in current page </h2>
            <div class="vedmg-instructor-summary">
                <span>Total Instructors: <strong><?php echo $total_instructors; ?></strong></span>
                <span>Showing: <strong><?php echo count($instructors); ?></strong></span>
                <span>Page: <strong><?php echo $current_page; ?> of <?php echo $total_pages; ?></strong></span>
            </div>
        </div>
        
        <!-- Bulk Actions -->
        <div class="vedmg-bulk-actions">
            <select id="instructor-bulk-action-select">
                <option value="">Bulk Actions</option>
                <option value="activate">Activate Selected</option>
                <option value="deactivate">Deactivate Selected</option>
                <option value="send_email">Send Email to Selected</option>
                <option value="export">Export Selected</option>
            </select>
            <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="apply-instructor-bulk-action">Apply</button>
        </div>
        
        <!-- Instructors Table -->
        <table class="vedmg-classroom-table">
            <thead>
                <tr>
                    <th><input type="checkbox" id="select-all-instructors"></th>
                    <th>Instructor Name</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Specialization</th>
                    <th>Courses</th>
                    <th>Students</th>
                    <th>Status</th>
                    <th>Last Activity</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($instructors)): ?>
                    <?php foreach ($instructors as $instructor): ?>
                        <tr class="real-data-row" data-instructor-id="<?php echo $instructor->instructor_id; ?>">
                            <td><input type="checkbox" class="instructor-checkbox" value="<?php echo $instructor->instructor_id; ?>"></td>
                            <td>
                                <strong><?php echo esc_html($instructor->instructor_name); ?></strong>
                                <small style="display: block; color: #666;">(Real Data)</small>
                            </td>
                            <td><?php echo esc_html($instructor->instructor_email); ?></td>
                            <td><?php echo esc_html($instructor->instructor_phone ?: 'Not provided'); ?></td>
                            <td><?php echo esc_html($instructor->specialization ?: 'Not specified'); ?></td>
                            <td>
                                <span class="vedmg-course-count"><?php echo intval($instructor->course_count); ?></span>
                                <a href="<?php echo admin_url('admin.php?page=vedmg-classroom-courses&instructor_id=' . $instructor->instructor_id); ?>" class="vedmg-view-courses">
                                    View Courses
                                </a>
                            </td>
                            <td><?php echo intval($instructor->student_count); ?></td>
                            <td>
                                <span class="vedmg-instructor-status" data-status="<?php echo $instructor->status; ?>">
                                    <?php echo VedMG_ClassRoom_Database_Helper::format_enrollment_status($instructor->status); ?>
                                </span>
                            </td>
                            <td><?php echo VedMG_ClassRoom_Database_Helper::format_date($instructor->last_activity); ?></td>
                            <td>
                                <div class="vedmg-action-buttons">
                                    <button class="vedmg-classroom-btn vedmg-view-instructor-btn" data-instructor-id="<?php echo $instructor->instructor_id; ?>">
                                        View Details
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
                
                <?php if (empty($instructors)): ?>
                    <tr>
                        <td colspan="10" style="text-align: center; padding: 20px; color: #666;">
                            <em>No instructors found in database. Sync with MasterStudy LMS to load instructor data.</em>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Instructor Edit Modal -->
<div id="vedmg-instructor-edit-modal" class="vedmg-modal" style="display: none;">
    <div class="vedmg-modal-content">
        <div class="vedmg-modal-header">
            <h3>Edit Instructor</h3>
            <span class="vedmg-modal-close">&times;</span>
        </div>
        <div class="vedmg-modal-body">
            <form id="vedmg-instructor-edit-form">
                <input type="hidden" id="edit-instructor-id" name="instructor_id" value="">
                
                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-instructor-name">Full Name:</label>
                        <input type="text" id="edit-instructor-name" name="instructor_name" class="vedmg-form-control" required>
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-instructor-email">Email Address:</label>
                        <input type="email" id="edit-instructor-email" name="instructor_email" class="vedmg-form-control" required>
                    </div>
                </div>
                
                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-instructor-phone">Phone Number:</label>
                        <input type="tel" id="edit-instructor-phone" name="instructor_phone" class="vedmg-form-control">
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-instructor-specialization">Specialization:</label>
                        <select id="edit-instructor-specialization" name="specialization" class="vedmg-form-control">
                            <option value="">Select Specialization</option>
                            <option value="Mathematics">Mathematics</option>
                            <option value="Science">Science</option>
                            <option value="Computer Science">Computer Science</option>
                            <option value="English">English</option>
                            <option value="History">History</option>
                            <option value="Physics">Physics</option>
                            <option value="Chemistry">Chemistry</option>
                            <option value="Biology">Biology</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                </div>
                
                <div class="vedmg-form-group">
                    <label for="edit-instructor-bio">Bio/Description:</label>
                    <textarea id="edit-instructor-bio" name="instructor_bio" class="vedmg-form-control" rows="4" placeholder="Brief description about the instructor..."></textarea>
                </div>
                
                <div class="vedmg-form-row">
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-instructor-status">Status:</label>
                        <select id="edit-instructor-status" name="status" class="vedmg-form-control">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="pending">Pending Approval</option>
                        </select>
                    </div>
                    <div class="vedmg-form-group vedmg-form-half">
                        <label for="edit-instructor-role">Role:</label>
                        <select id="edit-instructor-role" name="instructor_role" class="vedmg-form-control">
                            <option value="instructor">Instructor</option>
                            <option value="senior_instructor">Senior Instructor</option>
                            <option value="coordinator">Coordinator</option>
                            <option value="admin">Administrator</option>
                        </select>
                    </div>
                </div>
                
                <div class="vedmg-form-actions">
                    <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="cancel-instructor-edit">Cancel</button>
                    <button type="submit" class="vedmg-classroom-btn vedmg-classroom-btn-primary" id="save-instructor-edit">
                        <span class="vedmg-classroom-spinner"></span>
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Instructor Details Modal -->
<div id="vedmg-instructor-details-modal" class="vedmg-modal" style="display: none;">
    <div class="vedmg-modal-content">
        <div class="vedmg-modal-header">
            <h3>Instructor Details</h3>
            <span class="vedmg-modal-close">&times;</span>
        </div>
        <div class="vedmg-modal-body">
            <div class="vedmg-instructor-details">
                <div class="vedmg-instructor-profile">
                    <div class="vedmg-profile-avatar">
                        <div class="vedmg-avatar-placeholder"></div>
                    </div>
                    <div class="vedmg-profile-info">
                        <h4 id="details-instructor-name">--</h4>
                        <p id="details-instructor-title">--</p>
                        <span class="vedmg-instructor-status" id="details-instructor-status">--</span>
                    </div>
                </div>
                
                <div class="vedmg-details-grid">
                    <div class="vedmg-detail-item">
                        <label>Email:</label>
                        <span id="details-instructor-email">--</span>
                    </div>
                    <div class="vedmg-detail-item">
                        <label>Phone:</label>
                        <span id="details-instructor-phone">--</span>
                    </div>
                    <div class="vedmg-detail-item">
                        <label>Specialization:</label>
                        <span id="details-instructor-specialization">--</span>
                    </div>
                    <div class="vedmg-detail-item">
                        <label>Join Date:</label>
                        <span id="details-instructor-join-date">--</span>
                    </div>
                    <div class="vedmg-detail-item">
                        <label>Total Courses:</label>
                        <span id="details-instructor-courses">--</span>
                    </div>
                    <div class="vedmg-detail-item">
                        <label>Total Students:</label>
                        <span id="details-instructor-students">--</span>
                    </div>
                </div>
                
                <div class="vedmg-instructor-bio">
                    <label>Bio:</label>
                    <p id="details-instructor-bio">No bio available.</p>
                </div>
                
                <div class="vedmg-instructor-courses-list">
                    <h4>Assigned Courses</h4>
                    <div id="details-instructor-course-list">
                        <p>Loading courses...</p>
                    </div>
                </div>
                
                <!-- Today's Classes Section -->
                <div class="vedmg-instructor-today-classes">
                    <h4>Today's Classes</h4>
                    <div id="details-instructor-today-classes">
                        <p>Loading today's schedule...</p>
                    </div>
                </div>
                
                <!-- Upcoming Classes Section -->
                <div class="vedmg-instructor-upcoming-classes">
                    <h4>Upcoming Classes (Next 7 Days)</h4>
                    <div id="details-instructor-upcoming-classes">
                        <p>Loading upcoming schedule...</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="vedmg-modal-footer">
            <button class="vedmg-classroom-btn vedmg-classroom-btn-warning" data-action="reassign">Reassign Classes</button>
            <button class="vedmg-classroom-btn vedmg-classroom-btn-secondary">Send Email</button>
        </div>
    </div>
</div>

<!-- Class Reassignment Modal -->
<div id="vedmg-class-reassignment-modal" class="vedmg-modal" style="display: none;">
    <div class="vedmg-modal-content">
        <div class="vedmg-modal-header">
            <h3>Reassign Classes</h3>
            <span class="vedmg-modal-close">&times;</span>
        </div>
        <div class="vedmg-modal-body">
            <form id="vedmg-class-reassignment-form">
                <input type="hidden" id="reassign-from-instructor-id" name="from_instructor_id" value="">
                
                <div class="vedmg-reassignment-info">
                    <div class="vedmg-instructor-info">
                        <h4>Reassigning Classes From:</h4>
                        <div class="vedmg-instructor-card">
                            <div class="vedmg-instructor-avatar">
                                <div class="vedmg-avatar-placeholder" id="reassign-from-avatar">?</div>
                            </div>
                            <div class="vedmg-instructor-details">
                                <strong id="reassign-from-name">Loading...</strong>
                                <p id="reassign-from-email">Loading...</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="vedmg-form-group">
                    <label for="reassign-to-instructor">Reassign Selected Classes To:</label>
                    <select id="reassign-to-instructor" name="to_instructor_id" class="vedmg-form-control" required>
                        <option value="">Select Target Instructor</option>
                    </select>
                    <small class="vedmg-form-help">Choose which specific classes to reassign</small>
                </div>
                
                <div class="vedmg-reassignment-type">
                    <h4>Reassignment Options:</h4>
                    <div class="vedmg-reassignment-radio-group">
                        <label class="vedmg-radio-label">
                            <input type="radio" name="reassignment_type" value="all" checked>
                            <span class="vedmg-radio-text">Reassign All Future Classes</span>
                        </label>
                        <label class="vedmg-radio-label">
                            <input type="radio" name="reassignment_type" value="selected">
                            <span class="vedmg-radio-text">Reassign Selected Classes Only</span>
                        </label>
                    </div>
                </div>
                
                <div class="vedmg-reassignment-sessions">
                    <div class="vedmg-sessions-header">
                        <h4>Future Sessions:</h4>
                        <div class="vedmg-bulk-session-controls" style="display: none;">
                            <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary vedmg-btn-sm" id="select-all-sessions">Select All</button>
                            <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary vedmg-btn-sm" id="deselect-all-sessions">Deselect All</button>
                        </div>
                    </div>
                    <div id="reassignment-sessions-list">
                        <p>Loading sessions...</p>
                    </div>
                </div>
                
                <div class="vedmg-reassignment-meetings">
                    <div class="vedmg-meetings-header">
                        <h4>Assigned Meetings:</h4>
                        <div class="vedmg-bulk-meeting-controls" style="display: none;">
                            <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary vedmg-btn-sm" id="select-all-meetings">Select All</button>
                            <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary vedmg-btn-sm" id="deselect-all-meetings">Deselect All</button>
                        </div>
                    </div>
                    <div id="reassignment-meetings-list">
                        <p>Loading assigned meetings...</p>
                    </div>
                </div>
                
                <div class="vedmg-form-actions">
                    <button type="button" class="vedmg-classroom-btn vedmg-classroom-btn-secondary" id="cancel-class-reassignment">Cancel</button>
                    <button type="submit" class="vedmg-classroom-btn vedmg-classroom-btn-warning" id="confirm-class-reassignment">
                        <span class="vedmg-classroom-spinner"></span>
                        Reassign Classes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Pagination -->
<div class="vedmg-pagination">
    <div class="vedmg-pagination-info">
        Showing <?php echo $start_item; ?> to <?php echo $end_item; ?> of <?php echo $total_instructors; ?> instructors
    </div>
    
    <?php if ($total_pages > 1): ?>
    <div class="vedmg-pagination-controls">
        <!-- First Page -->
        <?php if ($current_page > 1): ?>
            <a href="<?php echo $base_url . '&paged=1'; ?>" class="vedmg-pagination-btn">
                <span>‹‹</span>
            </a>
        <?php else: ?>
            <span class="vedmg-pagination-btn disabled">‹‹</span>
        <?php endif; ?>
        
        <!-- Previous Page -->
        <?php if ($current_page > 1): ?>
            <a href="<?php echo $base_url . '&paged=' . ($current_page - 1); ?>" class="vedmg-pagination-btn">
                <span>‹</span>
            </a>
        <?php else: ?>
            <span class="vedmg-pagination-btn disabled">‹</span>
        <?php endif; ?>
        
        <!-- Page Numbers -->
        <div class="vedmg-pagination-numbers">
            <?php 
            $start_page = max(1, $current_page - 2);
            $end_page = min($total_pages, $start_page + 4);
            
            // Adjust start if we're near the end
            if ($end_page - $start_page < 4) {
                $start_page = max(1, $end_page - 4);
            }
            
            for ($i = $start_page; $i <= $end_page; $i++): 
                if ($i == $current_page): ?>
                    <span class="vedmg-pagination-btn active"><?php echo $i; ?></span>
                <?php else: ?>
                    <a href="<?php echo $base_url . '&paged=' . $i; ?>" class="vedmg-pagination-btn"><?php echo $i; ?></a>
                <?php endif;
            endfor; ?>
        </div>
        
        <!-- Next Page -->
        <?php if ($current_page < $total_pages): ?>
            <a href="<?php echo $base_url . '&paged=' . ($current_page + 1); ?>" class="vedmg-pagination-btn">
                <span>›</span>
            </a>
        <?php else: ?>
            <span class="vedmg-pagination-btn disabled">›</span>
        <?php endif; ?>
        
        <!-- Last Page -->
        <?php if ($current_page < $total_pages): ?>
            <a href="<?php echo $base_url . '&paged=' . $total_pages; ?>" class="vedmg-pagination-btn">
                <span>››</span>
            </a>
        <?php else: ?>
            <span class="vedmg-pagination-btn disabled">››</span>
        <?php endif; ?>
    </div>
    <?php endif; ?>
</div>

<style>
/* Modal Styles - High Priority */
.vedmg-modal {
    display: none !important;
    position: fixed !important;
    z-index: 999999 !important;
    left: 0 !important;
    top: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    backdrop-filter: blur(2px);
}

.vedmg-modal.vedmg-modal-show {
    display: block !important;
}

.vedmg-modal-content {
    background-color: #fff !important;
    margin: 50px auto !important;
    padding: 0 !important;
    border: none !important;
    border-radius: 8px !important;
    width: 90% !important;
    max-width: 800px !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
    animation: vedmg-modal-slideIn 0.3s ease-out;
    position: relative !important;
    z-index: 1000000 !important;
}

@keyframes vedmg-modal-slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.vedmg-modal-header {
    padding: 20px 25px !important;
    border-bottom: 1px solid #e9ecef !important;
    background: #f8f9fa !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

.vedmg-modal-header h3 {
    margin: 0 !important;
    color: #333 !important;
    font-size: 18px !important;
    font-weight: 600 !important;
}

.vedmg-modal-close {
    background: none !important;
    border: none !important;
    font-size: 24px !important;
    font-weight: bold !important;
    color: #999 !important;
    cursor: pointer !important;
    padding: 0 !important;
    width: 30px !important;
    height: 30px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 50% !important;
    transition: all 0.2s ease !important;
}

.vedmg-modal-close:hover {
    background: #f8f9fa !important;
    color: #333 !important;
}

.vedmg-modal-body {
    padding: 25px !important;
    max-height: 60vh !important;
    overflow-y: auto !important;
}

.vedmg-modal-footer {
    padding: 20px 25px !important;
    border-top: 1px solid #e9ecef !important;
    background: #f8f9fa !important;
    display: flex !important;
    gap: 10px !important;
    justify-content: flex-end !important;
}

/* Enhanced styling for instructor management */
.vedmg-instructor-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.vedmg-stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.vedmg-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.vedmg-stat-number {
    font-size: 2.5em;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.vedmg-stat-label {
    font-size: 14px;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.vedmg-stat-note {
    margin-top: 8px;
}

.placeholder-data {
    background: rgba(255,255,255,0.2);
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
}

/* Instructor table enhancements */
.vedmg-classroom-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.vedmg-classroom-table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.5px;
}

.vedmg-classroom-table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.002);
    transition: all 0.2s ease;
}

.vedmg-instructor-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.vedmg-instructor-status[data-status="active"] {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.vedmg-instructor-status[data-status="inactive"] {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.vedmg-instructor-status[data-status="pending"] {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Modal styling for instructor details */
.vedmg-instructor-details {
    max-height: 70vh;
    overflow-y: auto;
}

.vedmg-instructor-profile {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.vedmg-profile-avatar {
    margin-right: 20px;
}

.vedmg-avatar-placeholder {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    font-weight: bold;
}

.vedmg-profile-info h4 {
    margin: 0 0 5px 0;
    font-size: 24px;
    color: #333;
}

.vedmg-profile-info p {
    margin: 0 0 10px 0;
    color: #666;
    font-style: italic;
}

.vedmg-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.vedmg-detail-item {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.vedmg-detail-item label {
    display: block;
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.vedmg-detail-item span {
    color: #333;
    font-size: 14px;
}

.vedmg-instructor-bio {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
}

.vedmg-instructor-bio label {
    display: block;
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
    font-size: 14px;
}

.vedmg-instructor-bio p {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

.vedmg-instructor-courses-list h4 {
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

/* Today's Classes Styling */
.vedmg-instructor-today-classes {
    background: #fff3cd;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    border-left: 4px solid #ffc107;
}

.vedmg-instructor-today-classes h4 {
    color: #856404;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.vedmg-instructor-today-classes h4:before {
    content: "🕒";
    font-size: 16px;
}

.vedmg-today-class-item {
    background: white;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 10px;
    border: 1px solid #ffeaa7;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.vedmg-today-class-item:last-child {
    margin-bottom: 0;
}

.vedmg-class-time {
    font-weight: bold;
    color: #856404;
    font-size: 14px;
}

.vedmg-class-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 5px 0;
}

.vedmg-class-course {
    color: #666;
    font-size: 14px;
    margin-bottom: 5px;
}

.vedmg-class-students {
    color: #495057;
    font-size: 12px;
    background: #f8f9fa;
    padding: 2px 8px;
    border-radius: 12px;
    display: inline-block;
}

/* Upcoming Classes Styling */
.vedmg-instructor-upcoming-classes {
    background: #d1ecf1;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    border-left: 4px solid #17a2b8;
}

.vedmg-instructor-upcoming-classes h4 {
    color: #0c5460;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.vedmg-instructor-upcoming-classes h4:before {
    content: "📅";
    font-size: 16px;
}

.vedmg-upcoming-class-item {
    background: white;
    padding: 12px 15px;
    border-radius: 6px;
    margin-bottom: 8px;
    border: 1px solid #bee5eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.vedmg-upcoming-class-item:last-child {
    margin-bottom: 0;
}

.vedmg-upcoming-class-info {
    flex: 1;
}

.vedmg-upcoming-class-date {
    font-weight: bold;
    color: #0c5460;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.vedmg-upcoming-class-time {
    color: #495057;
    font-size: 13px;
    margin-top: 2px;
}

.vedmg-upcoming-class-title {
    color: #333;
    font-weight: 500;
    margin: 3px 0;
}

.vedmg-upcoming-class-course {
    color: #666;
    font-size: 12px;
}

/* Class Reassignment Modal Styling */
.vedmg-reassignment-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.vedmg-instructor-card {
    display: flex;
    align-items: center;
    gap: 15px;
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.vedmg-instructor-avatar {
    flex-shrink: 0;
}

.vedmg-instructor-avatar .vedmg-avatar-placeholder {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    font-weight: bold;
}

.vedmg-instructor-details strong {
    display: block;
    color: #333;
    font-size: 16px;
    margin-bottom: 2px;
}

.vedmg-instructor-details p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.vedmg-reassignment-sessions {
    background: #fff3cd;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #ffc107;
}

.vedmg-sessions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.vedmg-sessions-header h4 {
    color: #856404;
    margin: 0;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.vedmg-bulk-session-controls {
    display: flex;
    gap: 8px;
}

/* Reassignment Sessions and Meetings */
.vedmg-reassignment-sessions,
.vedmg-reassignment-meetings {
    background: #fff3cd;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #ffc107;
}

.vedmg-reassignment-meetings {
    background: #f0f8ff;
    border-left: 4px solid #0066cc;
}

.vedmg-sessions-header,
.vedmg-meetings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.vedmg-sessions-header h4,
.vedmg-meetings-header h4 {
    color: #856404;
    margin: 0;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.vedmg-meetings-header h4 {
    color: #003d7a;
}

.vedmg-bulk-session-controls,
.vedmg-bulk-meeting-controls {
    display: flex;
    gap: 8px;
}

.vedmg-meeting-reassign-item {
    background: white;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 8px;
    border: 1px solid #cce5f4;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
}

.vedmg-meeting-reassign-item:hover {
    background: #f8f9fa;
    border-color: #0066cc;
}

.vedmg-meeting-reassign-item:last-child {
    margin-bottom: 0;
}

.vedmg-meeting-reassign-item.disabled {
    opacity: 0.6;
    background: #f8f9fa;
}

.vedmg-meeting-reassign-checkbox {
    margin-right: 12px;
    transform: scale(1.2);
    cursor: pointer;
}

.vedmg-meeting-reassign-info {
    flex: 1;
}

.vedmg-meeting-reassign-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 3px;
}

.vedmg-meeting-reassign-details {
    font-size: 12px;
    color: #666;
    display: flex;
    gap: 15px;
}

.vedmg-meeting-reassign-date {
    font-weight: bold;
    color: #003d7a;
    font-size: 12px;
    text-align: right;
    min-width: 100px;
}

.vedmg-no-reassign-meetings {
    text-align: center;
    padding: 20px;
    color: #6c757d;
    font-style: italic;
    background: rgba(255,255,255,0.5);
    border-radius: 6px;
}

.vedmg-btn-sm {
    padding: 4px 8px !important;
    font-size: 11px !important;
}

.vedmg-reassignment-type {
    background: #e3f2fd;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #2196f3;
}

.vedmg-reassignment-type h4 {
    color: #1565c0;
    margin-bottom: 12px;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.vedmg-reassignment-radio-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.vedmg-radio-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 2px solid #e3f2fd;
    transition: all 0.2s ease;
}

.vedmg-radio-label:hover {
    border-color: #2196f3;
    background: #f8f9fa;
}

.vedmg-radio-label input[type="radio"] {
    margin-right: 10px;
    transform: scale(1.2);
}

.vedmg-radio-label input[type="radio"]:checked + .vedmg-radio-text {
    font-weight: 600;
    color: #1565c0;
}

.vedmg-session-reassign-item {
    background: white;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 8px;
    border: 1px solid #ffeaa7;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
}

.vedmg-session-reassign-item.disabled {
    opacity: 0.6;
    background: #f8f9fa;
}

.vedmg-session-reassign-item:last-child {
    margin-bottom: 0;
}

.vedmg-session-checkbox {
    margin-right: 12px;
    transform: scale(1.2);
    cursor: pointer;
}

.vedmg-session-reassign-info {
    flex: 1;
}

.vedmg-session-reassign-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 3px;
}

.vedmg-session-reassign-details {
    font-size: 12px;
    color: #666;
}

.vedmg-session-reassign-date {
    font-weight: bold;
    color: #856404;
    font-size: 12px;
    text-align: right;
    min-width: 100px;
}

.vedmg-form-help {
    display: block;
    margin-top: 5px;
    color: #6c757d;
    font-size: 12px;
    font-style: italic;
}

/* No classes message */
.vedmg-no-classes {
    text-align: center;
    padding: 20px;
    color: #6c757d;
    font-style: italic;
    background: rgba(255,255,255,0.5);
    border-radius: 6px;
}

.vedmg-modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Pagination Styles */
.vedmg-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 25px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.vedmg-pagination-info {
    color: #666;
    font-size: 14px;
}

.vedmg-pagination-controls {
    display: flex;
    align-items: center;
    gap: 5px;
}

.vedmg-pagination-btn {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-size: 14px;
    text-decoration: none;
    display: inline-block;
}

.vedmg-pagination-btn:hover:not(.disabled) {
    background: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
    text-decoration: none;
}

.vedmg-pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    color: #6c757d;
}

.vedmg-pagination-btn.active {
    background: #0073aa;
    color: white;
    border-color: #0073aa;
}

.vedmg-pagination-numbers {
    display: flex;
    gap: 2px;
}

.vedmg-pagination-size {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
    font-size: 14px;
}

.vedmg-pagination-size select {
    padding: 5px 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .vedmg-instructor-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .vedmg-stat-card {
        padding: 20px;
    }
    
    .vedmg-stat-number {
        font-size: 2em;
    }
    
    .vedmg-instructor-profile {
        flex-direction: column;
        text-align: center;
    }
    
    .vedmg-profile-avatar {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .vedmg-details-grid {
        grid-template-columns: 1fr;
    }
    
    .vedmg-pagination {
        flex-direction: column;
        gap: 15px;
    }
    
    .vedmg-pagination-controls {
        order: -1;
    }
}

@media (max-width: 480px) {
    .vedmg-instructor-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .vedmg-pagination-controls {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .vedmg-modal-footer {
        flex-direction: column;
    }
}
</style>

<script>
// Instructor management specific JavaScript
jQuery(document).ready(function($) {
    console.log('Instructor roster page loaded');
    
    // Update instructor counts
    updateInstructorCounts();
    
    function updateInstructorCounts() {
        var activeCount = $('.vedmg-instructor-status[data-status="active"]').length;
        var inactiveCount = $('.vedmg-instructor-status[data-status="inactive"]').length;
        
        $('#active-instructor-count').text(activeCount);
        $('#inactive-instructor-count').text(inactiveCount);
    }
    
    // Any instructor-specific JavaScript will be handled by instructors.js
});
</script>
