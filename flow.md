# VEDmg-classroom — Flow Documentation

**Plugin Name:** VEDmg-classroom  
**Author:** <PERSON><PERSON><PERSON>  
**Integrations:** Word<PERSON>ress, MasterStudy LMS, WooCommerce, Google Classroom, Google Meet  

---

## Overview

The **VEDmg-classroom** plugin provides seamless integration between **MasterStudy LMS**, **WooCommerce**, and **Google Classroom / Google Meet**.  
It automates course creation, student enrollment, meeting scheduling, and calendar invites for online classrooms.

This flow describes the **end-to-end process** from course creation to student participation.

---

## Flow Steps

### **1. Course Creation & Google Classroom Setup**
- **Action (Instructor/Teacher):**  
  Creates a course in **MasterStudy LMS**.
- **Plugin Behavior:**  
  1. Fetch course details from MasterStudy LMS (title, description, category, instructor info, etc.).
  2. Store these details in the plugin's **custom database table**.
  3. Call the **Google Classroom API** to create a corresponding Google Classroom for the course.
- **Expected Result:**  
  - A new Google Classroom is created with matched details.
  - Course–Classroom mapping is stored in the database.

---

### **2. Instructor Accepts Google Classroom Invitation**
- **Action (Instructor):**
  1. Logs into Google Classroom.
  2. Accepts the invitation to become a teacher for the newly created class.
  3. Accesses the class dashboard.
- **Meeting Link Setup:**
  - On the left sidebar, the instructor will see an **option to generate a meeting link** (Google Meet or Google Classroom link).
  - The instructor clicks **Generate Link** → copies it.
  - Navigates to **VEDmg-classroom → Admin Panel → Course Management**.
  - Opens the **Update/Generate Meeting Link** popup and pastes the copied link.
- **Plugin Behavior:**
  - The system auto-fetches **Instructor Name** based on the course ID.
  - Updates/saves the generated meeting link in the database for that course.
  - Displays the link in the **Class Sessions** page.
- **Expected Result:**
  - The meeting/classroom link is now visible and accessible for that course in the admin panel.

---

### **3. Student Purchases Course & Auto Enrollment**
- **Action (Student):**
  - Purchases the course through **WooCommerce** checkout.
- **Plugin Behavior:**
  1. Automatically enrolls the student into the corresponding **Google Classroom**.
  2. Sends a **Google Calendar invite** with class details and meeting link.
  3. Shows the student’s name in the **Enrolled Students** section in the admin panel.
- **Expected Result:**
  - Student appears in “Enrolled Students” list.
  - Student receives an email invite with join links.
  - Student is visible in Google Classroom People → Students tab.

---

### **4. Scheduling Class Sessions / Labs**
- **Action (Instructor in Admin Panel):**
  1. Navigates to **Enrolled Students Page** of a course.
  2. Clicks **Schedule Lab** button.
  3. In the popup form:
     - Selects **Date & Time**.
     - Selects **Recurrence Type**: Recurring / Single Session.
     - Selects Audience: **Single Student**, **Group**, or **Entire Class**.
  4. Confirms by clicking **Schedule**.
- **Plugin Behavior:**
  - Stores schedule details in the database.
  - Calls the **Google Meet API** to generate a unique meeting link.
  - Adds the meeting to **Google Calendar** for all selected participants.
- **Expected Result:**
  - New session details are stored in the database.
  - All selected participants receive a Google Calendar event with a join link.
  - Sessions appear in the **Class Sessions Page**.

---

## Data Flow Summary

| Step | Trigger Source       | Plugin Action | External API Calls |
|------|---------------------|---------------|--------------------|
| 1    | MasterStudy LMS     | Store & Create Classroom | Google Classroom API (create) |
| 2    | Instructor Action   | Save Meeting Link | — |
| 3    | WooCommerce Purchase| Auto-Enroll Student | Google Classroom API (enroll) + Google Calendar API (invite) |
| 4    | Instructor Schedule | Store Session & Create Meet | Google Meet API + Google Calendar API |




## simple flow

1. teacher will cerate a course in masterstudy lms plugin -> we will fetch the details of that which is needed for creating google classroom -> store that data in database table -> hit the api and create a classroom
2. instructor who created the course will go to google classroom -> from there he will seee the option and hw will click accept -> after accepting he will go inside and then on the left side of the screen he will see the link generation option he will generate the link either google meet or classroom -> he will copy that and paste in the plugin's admin pannel course management page's udate/generate meeting link button popup.
3. that popup will fetch the instructor name automatically by seeing the course -> and then it will be shown in the class sessions page
4. when student will purchase that course using woocommerce -> automatically he wil be enrolled in that classroom and a calander invide will get shared -> his name will be shown in the enrolled students page of admin pannel -> on that page there will be a popup to create or schedule meeting under schedule lab button -> on that popup instructor will enter the date and time and recurring and individual student or group or class wide selection -> after selecting all the details he will click schedule -> we will store that data in the database table and hit the api and create a google meet link with all those details and that class will be added in the student's calander.
