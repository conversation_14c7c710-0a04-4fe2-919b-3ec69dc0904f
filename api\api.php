<?php
/**
 * VedMG ClassRoom API Handler
 * 
 * This file handles all API-related operations for the plugin.
 * It serves as the central point for Google Classroom API and other external API calls.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

/**
 * VedMG ClassRoom API Class
 * 
 * Handles all API operations and external service integrations
 */
class VedMG_ClassRoom_API {
    
    /**
     * @var string Google Classroom API endpoint
     */
    private static $google_classroom_api = 'https://gclassroom-839391304260.us-central1.run.app';
    
    /**
     * @var string Google Classroom API key
     */
    private static $api_key = 'G$$gle@VedMG!@#';
    
    /**
     * Initialize API functionality
     */
    public static function init() {
        // Add AJAX handlers for API operations
        add_action('wp_ajax_vedmg_sync_google_classroom', array(__CLASS__, 'handle_sync_google_classroom'));
        add_action('wp_ajax_vedmg_get_all_instructors', array(__CLASS__, 'handle_get_all_instructors'));
        
        vedmg_log_info('API', 'API handlers initialized');
    }
    
    /**
     * Sync Google Classroom data for selected instructor
     * ENHANCED WORKFLOW:
     * 1. Instructor selection is mandatory (for course attribution)
     * 2. Email can be overridden (optional)
     * 3. Single word matching between LMS and Google Classroom courses
     * 4. All courses attributed to selected instructor regardless of email used
     */
    public static function handle_sync_google_classroom() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $instructor_email = sanitize_email($_POST['instructor_email']);
        $instructor_name = sanitize_text_field($_POST['instructor_name'] ?? '');
        $instructor_id = intval($_POST['instructor_id'] ?? 0);
        
        if (empty($instructor_email)) {
            wp_send_json_error('Instructor email is required');
            return;
        }
        
        if (empty($instructor_name) || $instructor_id <= 0) {
            wp_send_json_error('Valid instructor selection is required');
            return;
        }
        
        vedmg_log_info('API', 'Starting Google Classroom sync for: ' . $instructor_email . ' (Selected: ' . $instructor_name . ', ID: ' . $instructor_id . ')');
        
        try {
            // Step 1: Get selected instructor's courses from database
            $instructor_courses = self::get_instructor_courses_by_id($instructor_id);
            
            // Step 2: Fetch courses from Google Classroom API using provided email
            $google_courses = self::fetch_google_classroom_courses($instructor_email);
            
            if ($google_courses === false) {
                wp_send_json_error('Failed to fetch courses from Google Classroom API. Please check the email address or API connectivity.');
                return;
            }
            
            if (empty($google_courses)) {
                wp_send_json_error('No courses found in Google Classroom for this email address. Please verify the email or check if the instructor has any courses in Google Classroom.');
                return;
            }
            
            // Step 3: Match courses using single-word matching and update database
            $match_result = self::enhanced_match_and_update_courses($instructor_courses, $google_courses, $instructor_id, $instructor_name, $instructor_email);
            
            vedmg_log_info('API', 'Google Classroom sync completed', json_encode($match_result));
            
            wp_send_json_success(array(
                'message' => 'Google Classroom sync completed successfully',
                'instructor_email' => $instructor_email,
                'instructor_name' => $instructor_name,
                'database_courses' => count($instructor_courses),
                'google_courses' => count($google_courses),
                'matches_found' => $match_result['matches'],
                'courses_updated' => $match_result['updated'],
                'created' => $match_result['created'],
                'matched_details' => $match_result['details']
            ));
            
        } catch (Exception $e) {
            vedmg_log_error('API', 'Google Classroom sync failed', $e->getMessage());
            wp_send_json_error('Sync failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Handle get all instructors request
     */
    public static function handle_get_all_instructors() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'vedmg_classroom_nonce')) {
            wp_send_json_error('Security check failed');
        }
        
        vedmg_log_info('API', 'Getting all instructors for dropdown');
        
        try {
            // Get all instructors using database helper (same as instructor roster page)
            $instructors_data = VedMG_ClassRoom_Database_Helper::get_instructors_paginated(1, 1000, '', '');
            $instructors = $instructors_data['instructors'];
            
            // Format instructors for dropdown
            $formatted_instructors = array();
            foreach ($instructors as $instructor) {
                $formatted_instructors[] = array(
                    'instructor_id' => $instructor->instructor_id,
                    'instructor_name' => $instructor->instructor_name,
                    'instructor_email' => $instructor->instructor_email,
                    'specialization' => $instructor->specialization ?: 'General',
                    'course_count' => intval($instructor->course_count)
                );
            }
            
            vedmg_log_info('API', 'Found ' . count($formatted_instructors) . ' instructors');
            
            wp_send_json_success(array(
                'instructors' => $formatted_instructors,
                'message' => 'Instructors retrieved successfully'
            ));
            
        } catch (Exception $e) {
            vedmg_log_error('API', 'Failed to get instructors', $e->getMessage());
            wp_send_json_error('Failed to get instructors: ' . $e->getMessage());
        }
    }
    
    /**
     * Fetch courses from Google Classroom API for specific instructor
     * 
     * @param string $instructor_email Instructor email address
     * @return array|false Array of courses or false on failure
     */
    private static function fetch_google_classroom_courses($instructor_email) {
        $api_url = self::$google_classroom_api . '/list_courses';
        
        vedmg_log_info('API', 'Fetching courses from: ' . $api_url . ' for: ' . $instructor_email);
        
        $post_data = json_encode([
            'instructor_email' => $instructor_email
        ]);
        
        $response = wp_remote_post($api_url, array(
            'timeout' => 30,
            'headers' => array(
                'Content-Type' => 'application/json',
                'x-api-key' => self::$api_key,
                'Content-Length' => strlen($post_data)
            ),
            'body' => $post_data
        ));
        
        if (is_wp_error($response)) {
            vedmg_log_error('API', 'Failed to fetch Google Classroom courses', $response->get_error_message());
            return false;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        if ($response_code !== 200) {
            vedmg_log_error('API', 'Google Classroom API returned error code: ' . $response_code);
            vedmg_log_error('API', 'Response body: ' . $response_body);
            return false;
        }
        
        $courses_data = json_decode($response_body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            vedmg_log_error('API', 'Failed to parse Google Classroom API response');
            return false;
        }
        
        vedmg_log_info('API', 'Successfully fetched ' . count($courses_data) . ' courses from Google Classroom');
        
        return $courses_data;
    }
    
    /**
     * Sync courses to database
     * 
     * @param array $google_courses Courses from Google Classroom API
     * @param string $instructor_email Instructor email
     * @return array Sync results
     */
    private static function sync_courses_to_database($google_courses, $instructor_email) {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        $synced = 0;
        $new = 0;
        $updated = 0;
        
        // Get instructor data
        $instructor_data = self::get_instructor_by_email($instructor_email);
        
        foreach ($google_courses as $google_course) {
            $google_classroom_id = $google_course['id'] ?? '';
            $course_name = $google_course['name'] ?? '';
            $course_description = $google_course['descriptionHeading'] ?? '';
            $google_classroom_link = $google_course['alternateLink'] ?? '';
            
            // Check if course already exists
            $existing_course = $wpdb->get_row($wpdb->prepare(
                "SELECT course_id FROM $courses_table WHERE google_classroom_id = %s",
                $google_classroom_id
            ));
            
            if ($existing_course) {
                // Update existing course
                $update_result = $wpdb->update(
                    $courses_table,
                    array(
                        'course_name' => $course_name,
                        'course_description' => $course_description,
                        'instructor_email' => $instructor_email,
                        'google_classroom_link' => $google_classroom_link,
                        'classroom_status' => 'active',
                        'updated_date' => current_time('mysql')
                    ),
                    array('google_classroom_id' => $google_classroom_id),
                    array('%s', '%s', '%s', '%s', '%s', '%s'),
                    array('%s')
                );
                
                if ($update_result !== false) {
                    $updated++;
                    vedmg_log_info('API', 'Updated course: ' . $course_name);
                }
            } else {
                // Insert new course
                $insert_result = $wpdb->insert(
                    $courses_table,
                    array(
                        'google_classroom_id' => $google_classroom_id,
                        'course_name' => $course_name,
                        'course_description' => $course_description,
                        'instructor_id' => $instructor_data ? $instructor_data->instructor_id : 0,
                        'instructor_name' => $instructor_data ? $instructor_data->instructor_name : 'Unknown',
                        'instructor_email' => $instructor_email,
                        'google_classroom_link' => $google_classroom_link,
                        'classroom_status' => 'active',
                        'course_status' => 'published'
                    ),
                    array('%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%s')
                );
                
                if ($insert_result !== false) {
                    $new++;
                    vedmg_log_info('API', 'Inserted new course: ' . $course_name);
                }
            }
            
            $synced++;
        }
        
        return array(
            'synced' => $synced,
            'new' => $new,
            'updated' => $updated
        );
    }
    
    /**
     * Get instructor's courses from database by instructor ID
     * 
     * @param int $instructor_id Instructor ID
     * @return array Array of instructor's courses
     */
    private static function get_instructor_courses_by_id($instructor_id) {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        // Get all courses for this instructor ID, prioritizing pending status
        $instructor_courses = $wpdb->get_results($wpdb->prepare(
            "SELECT course_id, course_name, instructor_email, classroom_status, 
                    google_classroom_id, instructor_name, instructor_id
             FROM $courses_table 
             WHERE instructor_id = %d
             ORDER BY 
                CASE classroom_status 
                    WHEN 'pending' THEN 1 
                    WHEN 'created' THEN 2 
                    WHEN 'active' THEN 3 
                    ELSE 4 
                END",
            $instructor_id
        ));
        
        vedmg_log_info('API', 'Found ' . count($instructor_courses) . ' courses for instructor ID: ' . $instructor_id);
        
        return $instructor_courses ?: array();
    }
    
    /**
     * Get instructor's courses from database (prioritize pending courses)
     * 
     * @param string $instructor_email Instructor email
     * @return array Array of instructor's courses
     */
    private static function get_instructor_courses($instructor_email) {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        // Get all courses for this instructor, prioritizing pending status
        $instructor_courses = $wpdb->get_results($wpdb->prepare(
            "SELECT course_id, course_name, instructor_email, classroom_status, 
                    google_classroom_id, instructor_name
             FROM $courses_table 
             WHERE instructor_email = %s 
             ORDER BY 
                CASE classroom_status 
                    WHEN 'pending' THEN 1 
                    WHEN 'created' THEN 2 
                    WHEN 'active' THEN 3 
                    ELSE 4 
                END",
            $instructor_email
        ));
        
        vedmg_log_info('API', 'Found ' . count($instructor_courses) . ' courses for instructor: ' . $instructor_email);
        
        return $instructor_courses ?: array();
    }
    
    /**
     * Get pending courses for an instructor (Legacy method - keeping for compatibility)
     * 
     * @param string $instructor_email Instructor email
     * @return array Array of pending courses
     */
    private static function get_pending_courses($instructor_email) {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        $pending_courses = $wpdb->get_results($wpdb->prepare(
            "SELECT course_id, course_name, instructor_email 
             FROM $courses_table 
             WHERE instructor_email = %s 
             AND classroom_status = 'pending'",
            $instructor_email
        ));
        
        return $pending_courses ?: array();
    }
    
    /**
     * Enhanced match and update courses using single-word matching
     * Courses are attributed to selected instructor regardless of email used for API
     * 
     * @param array $instructor_courses Instructor's courses from database
     * @param array $google_courses Courses from Google Classroom API
     * @param int $instructor_id Selected instructor ID (for attribution)
     * @param string $instructor_name Selected instructor name (for attribution)
     * @param string $api_email Email used for Google Classroom API
     * @return array Match results
     */
    private static function enhanced_match_and_update_courses($instructor_courses, $google_courses, $instructor_id, $instructor_name, $api_email) {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        $matches = 0;
        $updated = 0;
        $created = 0;
        $details = array();
        $processed_google_courses = array();
        
        // Phase 1: Match existing database courses with Google Classroom courses using single-word matching
        foreach ($instructor_courses as $db_course) {
            foreach ($google_courses as $google_course) {
                $google_course_id = $google_course['id'];
                
                // Skip if this Google course was already matched
                if (in_array($google_course_id, $processed_google_courses)) {
                    continue;
                }
                
                // Use enhanced single-word matching
                $match_result = self::enhanced_single_word_match($db_course->course_name, $google_course['name']);
                
                if ($match_result['match']) {
                    // Update database with Google Classroom data
                    $update_result = $wpdb->update(
                        $courses_table,
                        array(
                            'google_classroom_id' => $google_course['id'],
                            'google_classroom_link' => $google_course['alternateLink'] ?? '',
                            'classroom_status' => 'active',
                            'course_description' => $google_course['descriptionHeading'] ?? $db_course->course_name,
                            'updated_date' => current_time('mysql')
                        ),
                        array('course_id' => $db_course->course_id),
                        array('%s', '%s', '%s', '%s', '%s'),
                        array('%d')
                    );
                    
                    if ($update_result !== false) {
                        $matches++;
                        $updated++;
                        $processed_google_courses[] = $google_course_id;
                        $details[] = array(
                            'action' => 'updated',
                            'db_course' => $db_course->course_name,
                            'google_course' => $google_course['name'],
                            'google_classroom_id' => $google_course['id'],
                            'matched_word' => $match_result['matched_word'],
                            'status_change' => $db_course->classroom_status . ' → active',
                            'attributed_to' => $instructor_name
                        );
                        
                        vedmg_log_info('API', 'Course matched and updated: ' . $db_course->course_name . ' → ' . $google_course['name'] . ' (matched on: ' . $match_result['matched_word'] . ')');
                    }
                    
                    break; // Move to next DB course after finding a match
                }
            }
        }
        
        // Phase 2: Create new records for unmatched Google Classroom courses (attributed to selected instructor)
        foreach ($google_courses as $google_course) {
            $google_course_id = $google_course['id'];
            
            // Skip if this Google course was already matched
            if (in_array($google_course_id, $processed_google_courses)) {
                continue;
            }
            
            // Check if this Google Classroom ID already exists in database
            $existing_course = $wpdb->get_row($wpdb->prepare(
                "SELECT course_id FROM $courses_table WHERE google_classroom_id = %s",
                $google_course_id
            ));
            
            if (!$existing_course) {
                // Create new course record attributed to selected instructor
                $insert_result = $wpdb->insert(
                    $courses_table,
                    array(
                        'google_classroom_id' => $google_course_id,
                        'course_name' => $google_course['name'],
                        'course_description' => $google_course['descriptionHeading'] ?? $google_course['name'],
                        'instructor_id' => $instructor_id,
                        'instructor_name' => $instructor_name,
                        'instructor_email' => $api_email, // Email used for API call
                        'google_classroom_link' => $google_course['alternateLink'] ?? '',
                        'classroom_status' => 'active',
                        'course_status' => 'published',
                        'created_date' => current_time('mysql'),
                        'updated_date' => current_time('mysql')
                    ),
                    array('%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
                );
                
                if ($insert_result !== false) {
                    $created++;
                    $details[] = array(
                        'action' => 'created',
                        'db_course' => 'New Course',
                        'google_course' => $google_course['name'],
                        'google_classroom_id' => $google_course['id'],
                        'matched_word' => 'N/A (new course)',
                        'status_change' => 'new → active',
                        'attributed_to' => $instructor_name
                    );
                    
                    vedmg_log_info('API', 'New course created from Google Classroom: ' . $google_course['name'] . ' (attributed to: ' . $instructor_name . ')');
                }
            }
        }
        
        return array(
            'matches' => $matches,
            'updated' => $updated,
            'created' => $created,
            'details' => $details
        );
    }
    
    /**
     * Match instructor courses with Google Classroom courses and update database
     * Enhanced version: Handle multiple course statuses and better matching
     * 
     * @param array $instructor_courses Instructor's courses from database
     * @param array $google_courses Courses from Google Classroom API
     * @param string $instructor_email Instructor email for validation
     * @param string $instructor_name Instructor name for new records
     * @return array Match results
     */
    private static function match_and_update_courses($instructor_courses, $google_courses, $instructor_email, $instructor_name) {
        global $wpdb;
        
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        $matches = 0;
        $updated = 0;
        $created = 0;
        $details = array();
        $processed_google_courses = array();
        
        // Phase 1: Match existing database courses with Google Classroom courses
        foreach ($instructor_courses as $db_course) {
            foreach ($google_courses as $google_course) {
                $google_course_id = $google_course['id'];
                
                // Skip if this Google course was already matched
                if (in_array($google_course_id, $processed_google_courses)) {
                    continue;
                }
                
                if (self::loose_match_course_names($db_course->course_name, $google_course['name'])) {
                    // Update database with Google Classroom data
                    $update_result = $wpdb->update(
                        $courses_table,
                        array(
                            'google_classroom_id' => $google_course['id'],
                            'google_classroom_link' => $google_course['alternateLink'] ?? '',
                            'classroom_status' => 'active',
                            'course_description' => $google_course['descriptionHeading'] ?? $db_course->course_name,
                            'updated_date' => current_time('mysql')
                        ),
                        array('course_id' => $db_course->course_id),
                        array('%s', '%s', '%s', '%s', '%s'),
                        array('%d')
                    );
                    
                    if ($update_result !== false) {
                        $matches++;
                        $updated++;
                        $processed_google_courses[] = $google_course_id;
                        $details[] = array(
                            'action' => 'updated',
                            'db_course' => $db_course->course_name,
                            'google_course' => $google_course['name'],
                            'google_classroom_id' => $google_course['id'],
                            'status_change' => $db_course->classroom_status . ' → active'
                        );
                        
                        vedmg_log_info('API', 'Course matched and updated: ' . $db_course->course_name . ' → ' . $google_course['name']);
                    }
                    
                    break; // Move to next DB course after finding a match
                }
            }
        }
        
        // Phase 2: Create new records for unmatched Google Classroom courses
        foreach ($google_courses as $google_course) {
            $google_course_id = $google_course['id'];
            
            // Skip if this Google course was already matched
            if (in_array($google_course_id, $processed_google_courses)) {
                continue;
            }
            
            // Check if this Google Classroom ID already exists in database
            $existing_course = $wpdb->get_row($wpdb->prepare(
                "SELECT course_id FROM $courses_table WHERE google_classroom_id = %s",
                $google_course_id
            ));
            
            if (!$existing_course) {
                // Get instructor data for new course
                $instructor_data = self::get_instructor_by_email($instructor_email);
                
                // Create new course record
                $insert_result = $wpdb->insert(
                    $courses_table,
                    array(
                        'google_classroom_id' => $google_course_id,
                        'course_name' => $google_course['name'],
                        'course_description' => $google_course['descriptionHeading'] ?? $google_course['name'],
                        'instructor_id' => $instructor_data ? $instructor_data->instructor_id : 0,
                        'instructor_name' => $instructor_data ? $instructor_data->instructor_name : $instructor_name,
                        'instructor_email' => $instructor_email,
                        'google_classroom_link' => $google_course['alternateLink'] ?? '',
                        'classroom_status' => 'active',
                        'course_status' => 'published',
                        'created_date' => current_time('mysql'),
                        'updated_date' => current_time('mysql')
                    ),
                    array('%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
                );
                
                if ($insert_result !== false) {
                    $created++;
                    $details[] = array(
                        'action' => 'created',
                        'db_course' => 'New Course',
                        'google_course' => $google_course['name'],
                        'google_classroom_id' => $google_course['id'],
                        'status_change' => 'new → active'
                    );
                    
                    vedmg_log_info('API', 'New course created from Google Classroom: ' . $google_course['name']);
                }
            }
        }
        
        return array(
            'matches' => $matches,
            'updated' => $updated,
            'created' => $created,
            'details' => $details
        );
    }
    
    /**
     * Enhanced single-word matching between LMS and Google Classroom courses
     * If any single meaningful word matches, consider courses the same
     * 
     * @param string $lms_course_name LMS course name
     * @param string $google_course_name Google Classroom course name
     * @return array Match result with details
     */
    private static function enhanced_single_word_match($lms_course_name, $google_course_name) {
        // Convert to lowercase and clean
        $lms_name = strtolower(trim($lms_course_name));
        $google_name = strtolower(trim($google_course_name));
        
        // Remove common words that don't help with matching
        $common_words = ['the', 'of', 'and', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'course', 'class', 'introduction', 'basic', 'advanced'];
        
        // Extract meaningful words (length > 2)
        $lms_words = array_filter(explode(' ', $lms_name), function($word) use ($common_words) {
            return strlen(trim($word)) > 2 && !in_array(trim($word), $common_words);
        });
        
        $google_words = array_filter(explode(' ', $google_name), function($word) use ($common_words) {
            return strlen(trim($word)) > 2 && !in_array(trim($word), $common_words);
        });
        
        // Clean up words
        $lms_words = array_map('trim', $lms_words);
        $google_words = array_map('trim', $google_words);
        
        // Check for any single word match
        foreach ($lms_words as $lms_word) {
            foreach ($google_words as $google_word) {
                if ($lms_word === $google_word || 
                    strpos($lms_word, $google_word) !== false || 
                    strpos($google_word, $lms_word) !== false) {
                    return array(
                        'match' => true,
                        'matched_word' => $lms_word . ' ↔ ' . $google_word,
                        'confidence' => 'high'
                    );
                }
            }
        }
        
        return array(
            'match' => false,
            'matched_word' => null,
            'confidence' => 'none'
        );
    }
    
    /**
     * Loose matching algorithm for course names
     * 
     * @param string $db_course_name Database course name
     * @param string $google_course_name Google Classroom course name
     * @return bool True if courses match
     */
    private static function loose_match_course_names($db_course_name, $google_course_name) {
        $db_name = strtolower(trim($db_course_name));
        $google_name = strtolower(trim($google_course_name));
        
        // Remove common words
        $common_words = ['the', 'of', 'and', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'fundamentals', 'basics', 'introduction', 'advanced', 'course', 'program', 'computing'];
        
        foreach ($common_words as $word) {
            $db_name = str_replace(' ' . $word . ' ', ' ', ' ' . $db_name . ' ');
            $google_name = str_replace(' ' . $word . ' ', ' ', ' ' . $google_name . ' ');
            $db_name = str_replace(' ' . $word, '', $db_name);
            $google_name = str_replace(' ' . $word, '', $google_name);
            $db_name = str_replace($word . ' ', '', $db_name);
            $google_name = str_replace($word . ' ', '', $google_name);
        }
        
        $db_name = trim(preg_replace('/\s+/', ' ', $db_name));
        $google_name = trim(preg_replace('/\s+/', ' ', $google_name));
        
        // Direct substring check for short names
        if (strlen($google_name) <= 15 && strpos($db_name, $google_name) !== false) {
            return true;
        }
        
        if (strlen($db_name) <= 15 && strpos($google_name, $db_name) !== false) {
            return true;
        }
        
        // Word-based matching
        $db_words = array_filter(explode(' ', $db_name), function($word) { return strlen($word) > 2; });
        $google_words = array_filter(explode(' ', $google_name), function($word) { return strlen($word) > 2; });
        
        $matching_words = 0;
        $total_db_words = count($db_words);
        
        foreach ($db_words as $db_word) {
            foreach ($google_words as $google_word) {
                if ($db_word === $google_word || 
                    strpos($google_word, $db_word) !== false || 
                    strpos($db_word, $google_word) !== false) {
                    $matching_words++;
                    break;
                }
            }
        }
        
        $similarity_percentage = $total_db_words > 0 ? ($matching_words / $total_db_words) * 100 : 0;
        
        return $similarity_percentage >= 60; // 60% similarity threshold
    }
    
    /**
     * Get instructor data by email
     * 
     * @param string $instructor_email Instructor email
     * @return object|null Instructor data or null if not found
     */
    private static function get_instructor_by_email($instructor_email) {
        global $wpdb;
        
        // Try to get instructor from existing data
        $courses_table = $wpdb->prefix . 'vedmg_courses';
        
        $instructor = $wpdb->get_row($wpdb->prepare(
            "SELECT DISTINCT instructor_id, instructor_name, instructor_email 
             FROM $courses_table 
             WHERE instructor_email = %s 
             AND instructor_id > 0 
             LIMIT 1",
            $instructor_email
        ));
        
        return $instructor;
    }
}

// Initialize API handlers
VedMG_ClassRoom_API::init();
?>
