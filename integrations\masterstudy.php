<?php
/**
 * VedMG ClassRoom MasterStudy LMS Integration
 * 
 * This file handles integration with MasterStudy LMS for automatic
 * course data synchronization when instructors create courses.
 * 
 * @package VedMG_ClassRoom
 * <AUTHOR>
 * @version 1.0
 */

// Prevent direct access to this file
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

/**
 * VedMG ClassRoom MasterStudy Integration Class
 * 
 * Handles integration with MasterStudy LMS for course synchronization
 */
class VedMG_ClassRoom_MasterStudy_Integration {
    
    /**
     * Initialize MasterStudy integration
     */
    public static function init() {
        // Hook into MasterStudy course creation
        add_action('stm_lms_course_created', array(__CLASS__, 'handle_course_created'), 10, 1);
        add_action('stm_lms_course_updated', array(__CLASS__, 'handle_course_updated'), 10, 1);
        add_action('stm_lms_course_published', array(__CLASS__, 'handle_course_published'), 10, 1);
        
        // Hook into WordPress post save for courses
        add_action('save_post', array(__CLASS__, 'handle_course_save'), 10, 3);
        
        vedmg_log_info('MASTERSTUDY', 'MasterStudy LMS integration initialized');
    }
    
    /**
     * Handle course created event
     */
    public static function handle_course_created($course_id) {
        vedmg_log_info('MASTERSTUDY', 'Course created event: ' . $course_id);
        
        try {
            self::sync_course_to_vedmg($course_id, 'created');
        } catch (Exception $e) {
            vedmg_log_error('MASTERSTUDY', 'Failed to sync created course', $e->getMessage());
        }
    }
    
    /**
     * Handle course updated event
     */
    public static function handle_course_updated($course_id) {
        vedmg_log_info('MASTERSTUDY', 'Course updated event: ' . $course_id);
        
        try {
            self::sync_course_to_vedmg($course_id, 'updated');
        } catch (Exception $e) {
            vedmg_log_error('MASTERSTUDY', 'Failed to sync updated course', $e->getMessage());
        }
    }
    
    /**
     * Handle course published event
     */
    public static function handle_course_published($course_id) {
        vedmg_log_info('MASTERSTUDY', 'Course published event: ' . $course_id);
        
        try {
            self::sync_course_to_vedmg($course_id, 'published');
        } catch (Exception $e) {
            vedmg_log_error('MASTERSTUDY', 'Failed to sync published course', $e->getMessage());
        }
    }
    
    /**
     * Handle course save via WordPress hook
     */
    public static function handle_course_save($post_id, $post, $update) {
        // Check if this is a MasterStudy course
        if ($post->post_type !== 'stm-courses') {
            return;
        }
        
        // Skip if this is an auto-save or revision
        if (wp_is_post_autosave($post_id) || wp_is_post_revision($post_id)) {
            return;
        }
        
        vedmg_log_info('MASTERSTUDY', 'Course save event: ' . $post_id . ' (update: ' . ($update ? 'yes' : 'no') . ')');
        
        try {
            self::sync_course_to_vedmg($post_id, $update ? 'updated' : 'created');
        } catch (Exception $e) {
            vedmg_log_error('MASTERSTUDY', 'Failed to sync course on save', $e->getMessage());
        }
    }
    
    /**
     * Sync course data to VedMG database
     */
    private static function sync_course_to_vedmg($masterstudy_course_id, $action) {
        global $wpdb;
        
        // Get course data from MasterStudy
        $course_data = self::get_masterstudy_course_data($masterstudy_course_id);
        if (!$course_data) {
            throw new Exception('Course data not found: ' . $masterstudy_course_id);
        }
        
        // Check if course already exists in VedMG database
        $existing_course = $wpdb->get_row($wpdb->prepare("
            SELECT * FROM {$wpdb->prefix}vedmg_courses 
            WHERE masterstudy_course_id = %d
        ", $masterstudy_course_id));
        
        // Prepare course data for VedMG database
        $vedmg_course_data = array(
            'masterstudy_course_id' => $masterstudy_course_id,
            'course_name' => $course_data['name'],
            'course_description' => $course_data['description'],
            'instructor_id' => $course_data['instructor_id'],
            'instructor_name' => $course_data['instructor_name'],
            'classroom_status' => 'pending', // Default status
            'auto_enroll_enabled' => 1,
            'updated_date' => current_time('mysql')
        );
        
        if ($existing_course) {
            // Update existing course
            $wpdb->update(
                $wpdb->prefix . 'vedmg_courses',
                $vedmg_course_data,
                array('course_id' => $existing_course->course_id),
                array('%d', '%s', '%s', '%d', '%s', '%s', '%d', '%s'),
                array('%d')
            );
            
            vedmg_log_info('MASTERSTUDY', 'Updated course in VedMG database: ' . $masterstudy_course_id);
        } else {
            // Insert new course
            $vedmg_course_data['created_date'] = current_time('mysql');
            
            $wpdb->insert(
                $wpdb->prefix . 'vedmg_courses',
                $vedmg_course_data,
                array('%d', '%s', '%s', '%d', '%s', '%s', '%d', '%s', '%s')
            );
            
            $new_course_id = $wpdb->insert_id;
            
            vedmg_log_info('MASTERSTUDY', 'Created new course in VedMG database: ' . $masterstudy_course_id . ' (VedMG ID: ' . $new_course_id . ')');
            
            // Trigger classroom creation if auto-creation is enabled
            if (get_option('vedmg_classroom_auto_create', false)) {
                self::trigger_classroom_creation($new_course_id);
            }
        }
    }
    
    /**
     * Get course data from MasterStudy
     */
    private static function get_masterstudy_course_data($course_id) {
        $post = get_post($course_id);
        if (!$post || $post->post_type !== 'stm-courses') {
            return false;
        }
        
        // Get instructor information
        $instructor_id = get_post_meta($course_id, 'course_instructor', true);
        $instructor_name = '';
        
        if ($instructor_id) {
            $instructor = get_userdata($instructor_id);
            if ($instructor) {
                $instructor_name = $instructor->display_name;
            }
        }
        
        return array(
            'name' => $post->post_title,
            'description' => $post->post_content,
            'instructor_id' => $instructor_id ?: 0,
            'instructor_name' => $instructor_name,
            'status' => $post->post_status,
            'created_date' => $post->post_date,
            'modified_date' => $post->post_modified
        );
    }
    
    /**
     * Trigger classroom creation for new course
     */
    private static function trigger_classroom_creation($vedmg_course_id) {
        // This would trigger API call to create Google Classroom
        // For now, just log the action
        vedmg_log_info('CLASSROOM', 'Triggered classroom creation for course: ' . $vedmg_course_id);
        
        // In the future, this would:
        // 1. Call Google Classroom API to create classroom
        // 2. Update the course record with Google Classroom ID
        // 3. Set classroom_status to 'created'
    }
    
    /**
     * Manual sync function for admin use
     */
    public static function manual_sync_all_courses() {
        global $wpdb;
        
        // Get all MasterStudy courses
        $courses = get_posts(array(
            'post_type' => 'stm-courses',
            'post_status' => array('publish', 'draft'),
            'numberposts' => -1
        ));
        
        $synced_count = 0;
        $errors = array();
        
        foreach ($courses as $course) {
            try {
                self::sync_course_to_vedmg($course->ID, 'manual_sync');
                $synced_count++;
            } catch (Exception $e) {
                $errors[] = 'Course ' . $course->ID . ': ' . $e->getMessage();
            }
        }
        
        vedmg_log_info('MASTERSTUDY', 'Manual sync completed. Synced: ' . $synced_count . ', Errors: ' . count($errors));
        
        return array(
            'synced' => $synced_count,
            'errors' => $errors,
            'total' => count($courses)
        );
    }
    
    /**
     * Get course statistics
     */
    public static function get_course_statistics() {
        global $wpdb;
        
        // Count MasterStudy courses
        $masterstudy_courses = $wpdb->get_var("
            SELECT COUNT(*) FROM {$wpdb->posts} 
            WHERE post_type = 'stm-courses' AND post_status IN ('publish', 'draft')
        ");
        
        // Count VedMG courses
        $vedmg_courses = $wpdb->get_var("
            SELECT COUNT(*) FROM {$wpdb->prefix}vedmg_courses
        ");
        
        // Count courses with classrooms
        $courses_with_classrooms = $wpdb->get_var("
            SELECT COUNT(*) FROM {$wpdb->prefix}vedmg_courses 
            WHERE google_classroom_id IS NOT NULL AND google_classroom_id != ''
        ");
        
        return array(
            'masterstudy_courses' => intval($masterstudy_courses),
            'vedmg_courses' => intval($vedmg_courses),
            'courses_with_classrooms' => intval($courses_with_classrooms),
            'sync_needed' => intval($masterstudy_courses) - intval($vedmg_courses)
        );
    }
    
    /**
     * Check if MasterStudy LMS is active
     */
    public static function is_masterstudy_active() {
        return class_exists('MasterStudy\Lms\Plugin') || function_exists('stm_lms_templates');
    }
    
    /**
     * Get MasterStudy version
     */
    public static function get_masterstudy_version() {
        if (defined('STM_LMS_VERSION')) {
            return STM_LMS_VERSION;
        }
        return 'Unknown';
    }
    
    /**
     * Manual sync all MasterStudy courses
     * Called when sync button is clicked
     */
    public static function manual_sync_all() {
        global $wpdb;
        
        vedmg_log_info('MASTERSTUDY', 'Manual sync started');
        
        try {
            // 1. Check if MasterStudy LMS tables exist
            $posts_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->posts}'");
            if (!$posts_table_exists) {
                return array(
                    'success' => false,
                    'message' => 'MasterStudy LMS tables not found in database'
                );
            }
            
            // 2. Check if MasterStudy LMS is active
            if (!self::is_masterstudy_active()) {
                return array(
                    'success' => false,
                    'message' => 'MasterStudy LMS plugin is not active'
                );
            }
            
            // 3. Ensure our custom tables exist
            require_once VEDMG_CLASSROOM_PLUGIN_DIR . 'database/activator.php';
            if (!VedMG_ClassRoom_Database_Activator::verify_tables_exist()) {
                // Create tables if they don't exist
                VedMG_ClassRoom_Database_Activator::activate();
                vedmg_log_info('MASTERSTUDY', 'Created missing custom tables during sync');
            }
            
            // 4. Get all MasterStudy courses
            $masterstudy_courses = $wpdb->get_results("
                SELECT 
                    p.ID as course_id,
                    p.post_title as course_name,
                    p.post_content as course_description,
                    p.post_author as instructor_id,
                    u.display_name as instructor_name,
                    p.post_status,
                    p.post_date as created_date,
                    p.post_modified as modified_date
                FROM {$wpdb->posts} p
                LEFT JOIN {$wpdb->users} u ON p.post_author = u.ID
                WHERE p.post_type = 'stm-courses'
                AND p.post_status IN ('publish', 'draft', 'private')
                ORDER BY p.post_date DESC
            ");
            
            if (empty($masterstudy_courses)) {
                return array(
                    'success' => true,
                    'message' => 'No courses found in MasterStudy LMS',
                    'synced_count' => 0,
                    'updated_count' => 0,
                    'new_count' => 0
                );
            }
            
            $synced_count = 0;
            $updated_count = 0;
            $new_count = 0;
            
            // 5. Sync each course to our custom table
            foreach ($masterstudy_courses as $course) {
                $existing_course = $wpdb->get_row($wpdb->prepare("
                    SELECT course_id, updated_date FROM {$wpdb->prefix}vedmg_courses 
                    WHERE masterstudy_course_id = %d
                ", $course->course_id));
                
                $course_data = array(
                    'masterstudy_course_id' => $course->course_id,
                    'course_name' => $course->course_name,
                    'course_description' => $course->course_description,
                    'instructor_id' => $course->instructor_id ?: 0,
                    'instructor_name' => $course->instructor_name ?: 'Unknown',
                    'updated_date' => current_time('mysql')
                );
                
                if ($existing_course) {
                    // Update existing course
                    $wpdb->update(
                        $wpdb->prefix . 'vedmg_courses',
                        $course_data,
                        array('course_id' => $existing_course->course_id),
                        array('%d', '%s', '%s', '%d', '%s', '%s'),
                        array('%d')
                    );
                    $updated_count++;
                    vedmg_log_info('MASTERSTUDY', 'Updated course: ' . $course->course_name);
                } else {
                    // Insert new course
                    $course_data['created_date'] = current_time('mysql');
                    $course_data['classroom_status'] = 'pending';
                    $course_data['auto_enroll_enabled'] = 1;
                    
                    $wpdb->insert(
                        $wpdb->prefix . 'vedmg_courses',
                        $course_data,
                        array('%d', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%d')
                    );
                    $new_count++;
                    vedmg_log_info('MASTERSTUDY', 'Created new course: ' . $course->course_name);
                }
                
                $synced_count++;
            }
            
            // 6. Update last sync time
            update_option('vedmg_classroom_last_sync_masterstudy', time());
            
            vedmg_log_info('MASTERSTUDY', "Manual sync completed: $synced_count courses processed ($new_count new, $updated_count updated)");
            
            return array(
                'success' => true,
                'message' => "Successfully synced $synced_count courses from MasterStudy LMS ($new_count new, $updated_count updated)",
                'synced_count' => $synced_count,
                'updated_count' => $updated_count,
                'new_count' => $new_count
            );
            
        } catch (Exception $e) {
            vedmg_log_error('MASTERSTUDY', 'Manual sync failed', $e->getMessage());
            return array(
                'success' => false,
                'message' => 'Sync failed: ' . $e->getMessage()
            );
        }
    }
}

// Initialize MasterStudy integration only if MasterStudy is active
add_action('plugins_loaded', function() {
    if (VedMG_ClassRoom_MasterStudy_Integration::is_masterstudy_active()) {
        VedMG_ClassRoom_MasterStudy_Integration::init();
        vedmg_log_info('INTEGRATION', 'MasterStudy LMS integration activated');
    } else {
        vedmg_log_warning('INTEGRATION', 'MasterStudy LMS not detected - integration disabled');
    }
});
